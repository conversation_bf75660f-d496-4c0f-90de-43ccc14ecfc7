{"logs": [{"outputFile": "com.masjid.clock.app-mergeDebugResources-55:/values-kk/values-kk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\9fd9d7009dd2359dbdb604cdcc345b0a\\transformed\\jetified-ui-1.4.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,386,488,575,656,749,839,921,990,1058,1141,1226,1299,1375,1445", "endColumns": "92,82,104,101,86,80,92,89,81,68,67,82,84,72,75,69,117", "endOffsets": "193,276,381,483,570,651,744,834,916,985,1053,1136,1221,1294,1370,1440,1558"}, "to": {"startLines": "41,42,43,44,45,49,50,101,102,103,104,106,107,109,111,112,113", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3715,3808,3891,3996,4098,4414,4495,8469,8559,8641,8710,8862,8945,9112,9286,9362,9432", "endColumns": "92,82,104,101,86,80,92,89,81,68,67,82,84,72,75,69,117", "endOffsets": "3803,3886,3991,4093,4180,4490,4583,8554,8636,8705,8773,8940,9025,9180,9357,9427,9545"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\f7c8c22bdafe0bf1b7b8f05029141cd9\\transformed\\core-1.9.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "110", "startColumns": "4", "startOffsets": "9185", "endColumns": "100", "endOffsets": "9281"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\6afa17b2b3e5aa97934ff0de89083c5d\\transformed\\material-1.8.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,351,427,506,600,688,780,892,974,1038,1133,1203,1266,1373,1438,1505,1566,1633,1695,1749,1863,1922,1983,2037,2112,2238,2326,2415,2557,2629,2702,2791,2848,2904,2970,3041,3118,3204,3276,3352,3433,3503,3590,3662,3753,3846,3920,3995,4087,4139,4205,4289,4375,4437,4501,4564,4668,4768,4862,4963,5024,5084", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,77,75,78,93,87,91,111,81,63,94,69,62,106,64,66,60,66,61,53,113,58,60,53,74,125,87,88,141,71,72,88,56,55,65,70,76,85,71,75,80,69,86,71,90,92,73,74,91,51,65,83,85,61,63,62,103,99,93,100,60,59,83", "endOffsets": "268,346,422,501,595,683,775,887,969,1033,1128,1198,1261,1368,1433,1500,1561,1628,1690,1744,1858,1917,1978,2032,2107,2233,2321,2410,2552,2624,2697,2786,2843,2899,2965,3036,3113,3199,3271,3347,3428,3498,3585,3657,3748,3841,3915,3990,4082,4134,4200,4284,4370,4432,4496,4559,4663,4763,4857,4958,5019,5079,5163"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,46,47,48,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,105", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3014,3092,3168,3247,3341,3429,3521,3633,4185,4249,4344,4588,4651,4758,4823,4890,4951,5018,5080,5134,5248,5307,5368,5422,5497,5623,5711,5800,5942,6014,6087,6176,6233,6289,6355,6426,6503,6589,6661,6737,6818,6888,6975,7047,7138,7231,7305,7380,7472,7524,7590,7674,7760,7822,7886,7949,8053,8153,8247,8348,8409,8778", "endLines": "5,33,34,35,36,37,38,39,40,46,47,48,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,105", "endColumns": "12,77,75,78,93,87,91,111,81,63,94,69,62,106,64,66,60,66,61,53,113,58,60,53,74,125,87,88,141,71,72,88,56,55,65,70,76,85,71,75,80,69,86,71,90,92,73,74,91,51,65,83,85,61,63,62,103,99,93,100,60,59,83", "endOffsets": "318,3087,3163,3242,3336,3424,3516,3628,3710,4244,4339,4409,4646,4753,4818,4885,4946,5013,5075,5129,5243,5302,5363,5417,5492,5618,5706,5795,5937,6009,6082,6171,6228,6284,6350,6421,6498,6584,6656,6732,6813,6883,6970,7042,7133,7226,7300,7375,7467,7519,7585,7669,7755,7817,7881,7944,8048,8148,8242,8343,8404,8464,8857"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\39edba29d1252d0b67d5e84f709fb54f\\transformed\\appcompat-1.6.1\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,318,428,513,619,738,818,895,986,1079,1174,1268,1368,1461,1556,1653,1744,1835,1916,2021,2124,2222,2329,2435,2535,2701,2796", "endColumns": "107,104,109,84,105,118,79,76,90,92,94,93,99,92,94,96,90,90,80,104,102,97,106,105,99,165,94,81", "endOffsets": "208,313,423,508,614,733,813,890,981,1074,1169,1263,1363,1456,1551,1648,1739,1830,1911,2016,2119,2217,2324,2430,2530,2696,2791,2873"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,431,536,646,731,837,956,1036,1113,1204,1297,1392,1486,1586,1679,1774,1871,1962,2053,2134,2239,2342,2440,2547,2653,2753,2919,9030", "endColumns": "107,104,109,84,105,118,79,76,90,92,94,93,99,92,94,96,90,90,80,104,102,97,106,105,99,165,94,81", "endOffsets": "426,531,641,726,832,951,1031,1108,1199,1292,1387,1481,1581,1674,1769,1866,1957,2048,2129,2234,2337,2435,2542,2648,2748,2914,3009,9107"}}]}]}