
plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
}

android {
    namespace "com.masjid.clock"
    compileSdkVersion 33

    defaultConfig {
        applicationId "com.masjid.clock"
        minSdkVersion 21
        targetSdkVersion 33
        versionCode 1
        versionName "1.0"
    }

    buildFeatures {
        compose true
    }

    composeOptions {
        kotlinCompilerExtensionVersion '1.2.0'
    }
}

dependencies {
    implementation 'androidx.core:core-ktx:1.9.0'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.8.0'
    implementation "androidx.compose.ui:ui:1.4.0"
    implementation "androidx.compose.material:material:1.4.0"
    implementation "androidx.compose.material3:material3:1.1.0"
    implementation "androidx.compose.ui:ui-tooling-preview:1.4.0"
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.6.1'
    implementation 'androidx.activity:activity-compose:1.7.0'

    implementation "androidx.datastore:datastore-preferences:1.0.0"
    implementation 'androidx.lifecycle:lifecycle-viewmodel-compose:2.5.1'

    // Prayer times calculation
    implementation 'com.batoulapps.adhan:adhan2:0.0.5'

    // Hijri calendar
    implementation 'com.github.msarhan:ummalqura-calendar:2.0.2'

    // For networking
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'

    // Image loading
    implementation 'io.coil-kt:coil-compose:2.4.0'

    // Color picker
    implementation 'com.github.skydoves:colorpicker-compose:1.0.4'

    // Navigation
    implementation "androidx.navigation:navigation-compose:2.6.0"

    // Permissions
    implementation "com.google.accompanist:accompanist-permissions:0.30.1"

    // System UI controller
    implementation "com.google.accompanist:accompanist-systemuicontroller:0.30.1"

    // Media player for Adhan
    implementation 'androidx.media:media:1.6.0'

    // Location services
    implementation 'com.google.android.gms:play-services-location:21.0.1'

    // Coroutines
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4'

    // StateFlow extensions
    implementation 'androidx.lifecycle:lifecycle-runtime-compose:2.6.1'
}
