{"logs": [{"outputFile": "com.masjid.clock.app-mergeDebugResources-55:/values-ky/values-ky.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\9fd9d7009dd2359dbdb604cdcc345b0a\\transformed\\jetified-ui-1.4.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,392,492,577,659,757,846,931,997,1064,1149,1236,1309,1388,1456", "endColumns": "92,83,109,99,84,81,97,88,84,65,66,84,86,72,78,67,117", "endOffsets": "193,277,387,487,572,654,752,841,926,992,1059,1144,1231,1304,1383,1451,1569"}, "to": {"startLines": "41,42,43,44,45,49,50,101,102,103,104,106,107,109,111,112,113", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3831,3924,4008,4118,4218,4531,4613,8597,8686,8771,8837,8986,9071,9240,9414,9493,9561", "endColumns": "92,83,109,99,84,81,97,88,84,65,66,84,86,72,78,67,117", "endOffsets": "3919,4003,4113,4213,4298,4608,4706,8681,8766,8832,8899,9066,9153,9308,9488,9556,9674"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\f7c8c22bdafe0bf1b7b8f05029141cd9\\transformed\\core-1.9.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "110", "startColumns": "4", "startOffsets": "9313", "endColumns": "100", "endOffsets": "9409"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\6afa17b2b3e5aa97934ff0de89083c5d\\transformed\\material-1.8.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,348,433,518,633,743,844,985,1069,1133,1227,1297,1358,1445,1508,1572,1631,1705,1767,1821,1938,1996,2057,2111,2185,2307,2391,2487,2619,2697,2775,2864,2925,2980,3046,3115,3192,3279,3351,3427,3509,3582,3667,3746,3836,3928,4002,4087,4177,4229,4294,4379,4464,4526,4590,4653,4770,4864,4964,5059,5124,5183", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,82,84,84,114,109,100,140,83,63,93,69,60,86,62,63,58,73,61,53,116,57,60,53,73,121,83,95,131,77,77,88,60,54,65,68,76,86,71,75,81,72,84,78,89,91,73,84,89,51,64,84,84,61,63,62,116,93,99,94,64,58,81", "endOffsets": "260,343,428,513,628,738,839,980,1064,1128,1222,1292,1353,1440,1503,1567,1626,1700,1762,1816,1933,1991,2052,2106,2180,2302,2386,2482,2614,2692,2770,2859,2920,2975,3041,3110,3187,3274,3346,3422,3504,3577,3662,3741,3831,3923,3997,4082,4172,4224,4289,4374,4459,4521,4585,4648,4765,4859,4959,5054,5119,5178,5260"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,46,47,48,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,105", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3027,3110,3195,3280,3395,3505,3606,3747,4303,4367,4461,4711,4772,4859,4922,4986,5045,5119,5181,5235,5352,5410,5471,5525,5599,5721,5805,5901,6033,6111,6189,6278,6339,6394,6460,6529,6606,6693,6765,6841,6923,6996,7081,7160,7250,7342,7416,7501,7591,7643,7708,7793,7878,7940,8004,8067,8184,8278,8378,8473,8538,8904", "endLines": "5,33,34,35,36,37,38,39,40,46,47,48,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,105", "endColumns": "12,82,84,84,114,109,100,140,83,63,93,69,60,86,62,63,58,73,61,53,116,57,60,53,73,121,83,95,131,77,77,88,60,54,65,68,76,86,71,75,81,72,84,78,89,91,73,84,89,51,64,84,84,61,63,62,116,93,99,94,64,58,81", "endOffsets": "310,3105,3190,3275,3390,3500,3601,3742,3826,4362,4456,4526,4767,4854,4917,4981,5040,5114,5176,5230,5347,5405,5466,5520,5594,5716,5800,5896,6028,6106,6184,6273,6334,6389,6455,6524,6601,6688,6760,6836,6918,6991,7076,7155,7245,7337,7411,7496,7586,7638,7703,7788,7873,7935,7999,8062,8179,8273,8373,8468,8533,8592,8981"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\39edba29d1252d0b67d5e84f709fb54f\\transformed\\appcompat-1.6.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,325,437,522,627,744,823,901,992,1085,1180,1274,1374,1467,1562,1657,1748,1839,1920,2026,2131,2229,2336,2439,2554,2715,2817", "endColumns": "110,108,111,84,104,116,78,77,90,92,94,93,99,92,94,94,90,90,80,105,104,97,106,102,114,160,101,81", "endOffsets": "211,320,432,517,622,739,818,896,987,1080,1175,1269,1369,1462,1557,1652,1743,1834,1915,2021,2126,2224,2331,2434,2549,2710,2812,2894"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,426,535,647,732,837,954,1033,1111,1202,1295,1390,1484,1584,1677,1772,1867,1958,2049,2130,2236,2341,2439,2546,2649,2764,2925,9158", "endColumns": "110,108,111,84,104,116,78,77,90,92,94,93,99,92,94,94,90,90,80,105,104,97,106,102,114,160,101,81", "endOffsets": "421,530,642,727,832,949,1028,1106,1197,1290,1385,1479,1579,1672,1767,1862,1953,2044,2125,2231,2336,2434,2541,2644,2759,2920,3022,9235"}}]}]}