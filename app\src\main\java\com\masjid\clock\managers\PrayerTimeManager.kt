package com.masjid.clock.managers

import com.batoulapps.adhan.*
import com.batoulapps.adhan.data.DateComponents
import com.masjid.clock.models.*
import java.util.*
import kotlin.math.abs

class PrayerTimeManager {
    
    fun calculatePrayerTimes(
        location: LocationData,
        date: Date = Date(),
        calculationMethod: CalculationMethodType = CalculationMethodType.UMM_AL_QURA,
        madhab: MadhabType = MadhabType.HANAFI,
        adjustments: PrayerAdjustments = PrayerAdjustments(),
        iqamaSettings: IqamaSettings = IqamaSettings()
    ): DailyPrayerTimes {
        
        val coordinates = Coordinates(location.latitude, location.longitude)
        val dateComponents = DateComponents.from(date)
        
        val params = getCalculationParameters(calculationMethod)
        params.madhab = when (madhab) {
            MadhabType.HANAFI -> Madhab.HANAFI
            MadhabType.SHAFI -> Madhab.SHAFI
        }
        
        val prayerTimes = PrayerTimes(coordinates, dateComponents, params)
        
        // Apply manual adjustments
        val fajrTime = Date(prayerTimes.fajr.time + (adjustments.fajr * 60 * 1000))
        val dhuhrTime = Date(prayerTimes.dhuhr.time + (adjustments.dhuhr * 60 * 1000))
        val asrTime = Date(prayerTimes.asr.time + (adjustments.asr * 60 * 1000))
        val maghribTime = Date(prayerTimes.maghrib.time + (adjustments.maghrib * 60 * 1000))
        val ishaTime = Date(prayerTimes.isha.time + (adjustments.isha * 60 * 1000))
        
        // Calculate iqama times
        val fajrIqama = Date(fajrTime.time + (iqamaSettings.fajr * 60 * 1000))
        val dhuhrIqama = Date(dhuhrTime.time + (iqamaSettings.dhuhr * 60 * 1000))
        val asrIqama = Date(asrTime.time + (iqamaSettings.asr * 60 * 1000))
        val maghribIqama = Date(maghribTime.time + (iqamaSettings.maghrib * 60 * 1000))
        val ishaIqama = Date(ishaTime.time + (iqamaSettings.isha * 60 * 1000))
        
        val now = System.currentTimeMillis()
        
        return DailyPrayerTimes(
            fajr = PrayerTimeData(
                name = "الفجر",
                time = fajrTime,
                iqamaTime = fajrIqama,
                adjustment = adjustments.fajr,
                isNext = fajrTime.time > now && isNextPrayer(fajrTime, listOf(dhuhrTime, asrTime, maghribTime, ishaTime)),
                isPassed = fajrTime.time < now
            ),
            sunrise = Date(prayerTimes.sunrise.time),
            dhuhr = PrayerTimeData(
                name = "الظهر",
                time = dhuhrTime,
                iqamaTime = dhuhrIqama,
                adjustment = adjustments.dhuhr,
                isNext = dhuhrTime.time > now && isNextPrayer(dhuhrTime, listOf(fajrTime, asrTime, maghribTime, ishaTime)),
                isPassed = dhuhrTime.time < now
            ),
            asr = PrayerTimeData(
                name = "العصر",
                time = asrTime,
                iqamaTime = asrIqama,
                adjustment = adjustments.asr,
                isNext = asrTime.time > now && isNextPrayer(asrTime, listOf(fajrTime, dhuhrTime, maghribTime, ishaTime)),
                isPassed = asrTime.time < now
            ),
            maghrib = PrayerTimeData(
                name = "المغرب",
                time = maghribTime,
                iqamaTime = maghribIqama,
                adjustment = adjustments.maghrib,
                isNext = maghribTime.time > now && isNextPrayer(maghribTime, listOf(fajrTime, dhuhrTime, asrTime, ishaTime)),
                isPassed = maghribTime.time < now
            ),
            isha = PrayerTimeData(
                name = "العشاء",
                time = ishaTime,
                iqamaTime = ishaIqama,
                adjustment = adjustments.isha,
                isNext = ishaTime.time > now && isNextPrayer(ishaTime, listOf(fajrTime, dhuhrTime, asrTime, maghribTime)),
                isPassed = ishaTime.time < now
            ),
            date = date
        )
    }
    
    private fun isNextPrayer(currentTime: Date, otherTimes: List<Date>): Boolean {
        val now = System.currentTimeMillis()
        if (currentTime.time <= now) return false
        
        return otherTimes.none { it.time in (now + 1) until currentTime.time }
    }
    
    private fun getCalculationParameters(method: CalculationMethodType): CalculationParameters {
        return when (method) {
            CalculationMethodType.UMM_AL_QURA -> CalculationMethod.UMM_AL_QURA.parameters
            CalculationMethodType.EGYPTIAN -> CalculationMethod.EGYPTIAN.parameters
            CalculationMethodType.KARACHI -> CalculationMethod.KARACHI.parameters
            CalculationMethodType.NORTH_AMERICA -> CalculationMethod.NORTH_AMERICA.parameters
            CalculationMethodType.MUSLIM_WORLD_LEAGUE -> CalculationMethod.MUSLIM_WORLD_LEAGUE.parameters
            CalculationMethodType.DUBAI -> CalculationMethod.DUBAI.parameters
            CalculationMethodType.MOON_SIGHTING_COMMITTEE -> CalculationMethod.MOON_SIGHTING_COMMITTEE.parameters
            CalculationMethodType.SINGAPORE -> CalculationMethod.SINGAPORE.parameters
            CalculationMethodType.TURKEY -> CalculationMethod.TURKEY.parameters
            CalculationMethodType.TEHRAN -> CalculationMethod.TEHRAN.parameters
            CalculationMethodType.CUSTOM -> CalculationMethod.OTHER.parameters
        }
    }
    
    fun getTimeUntilNextPrayer(prayerTimes: DailyPrayerTimes): Long {
        val nextPrayer = prayerTimes.getNextPrayer()
        return if (nextPrayer != null) {
            nextPrayer.time.time - System.currentTimeMillis()
        } else {
            // If no prayer left today, calculate time until Fajr tomorrow
            val tomorrow = Calendar.getInstance().apply {
                add(Calendar.DAY_OF_MONTH, 1)
            }.time
            
            val tomorrowPrayers = calculatePrayerTimes(
                LocationData(0.0, 0.0, ""), // This should be passed from outside
                tomorrow
            )
            tomorrowPrayers.fajr.time.time - System.currentTimeMillis()
        }
    }
    
    fun getTimeUntilIqama(prayerTimes: DailyPrayerTimes): Long? {
        val currentPrayer = prayerTimes.getCurrentOrNextPrayer()
        return if (currentPrayer != null && !currentPrayer.isPassed) {
            val now = System.currentTimeMillis()
            if (now >= currentPrayer.time.time && now < currentPrayer.iqamaTime.time) {
                currentPrayer.iqamaTime.time - now
            } else null
        } else null
    }
    
    fun formatTimeRemaining(milliseconds: Long): String {
        val totalSeconds = milliseconds / 1000
        val hours = totalSeconds / 3600
        val minutes = (totalSeconds % 3600) / 60
        val seconds = totalSeconds % 60
        
        return when {
            hours > 0 -> String.format("%02d:%02d:%02d", hours, minutes, seconds)
            minutes > 0 -> String.format("%02d:%02d", minutes, seconds)
            else -> String.format("00:%02d", seconds)
        }
    }
    
    fun isWithinIqamaTime(prayerTimes: DailyPrayerTimes): Boolean {
        return getTimeUntilIqama(prayerTimes) != null
    }
    
    fun getCurrentPrayerStatus(prayerTimes: DailyPrayerTimes): String {
        val now = System.currentTimeMillis()
        val currentPrayer = prayerTimes.getAllPrayers().lastOrNull { it.time.time <= now }
        
        return when {
            currentPrayer == null -> "قبل الفجر"
            isWithinIqamaTime(prayerTimes) -> "وقت الإقامة - ${currentPrayer.name}"
            else -> "بعد ${currentPrayer.name}"
        }
    }
}
