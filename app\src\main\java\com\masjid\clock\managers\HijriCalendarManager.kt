package com.masjid.clock.managers

import com.masjid.clock.models.HijriDate
import com.masjid.clock.models.GregorianDate
import java.util.*
import java.text.SimpleDateFormat
import kotlin.math.floor

class HijriCalendarManager {
    
    private val hijriMonthNames = arrayOf(
        "محرم", "صفر", "ربيع الأول", "ربيع الثاني", "جمادى الأولى", "جمادى الثانية",
        "رجب", "شعبان", "رمضان", "شوال", "ذو القعدة", "ذو الحجة"
    )
    
    private val arabicDayNames = arrayOf(
        "الأحد", "الاثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت"
    )
    
    private val gregorianMonthNames = arrayOf(
        "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
        "يوليو", "أغسطس", "سبتم<PERSON><PERSON>", "أكتوبر", "نوفمبر", "ديسمبر"
    )
    
    fun getCurrentHijriDate(): HijriDate {
        return gregorianToHijri(Date())
    }
    
    fun getCurrentGregorianDate(): GregorianDate {
        val calendar = Calendar.getInstance()
        val dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK)
        val dayName = arabicDayNames[(dayOfWeek - 1) % 7]
        
        return GregorianDate(
            day = calendar.get(Calendar.DAY_OF_MONTH),
            month = calendar.get(Calendar.MONTH) + 1,
            year = calendar.get(Calendar.YEAR),
            monthName = gregorianMonthNames[calendar.get(Calendar.MONTH)],
            dayName = dayName
        )
    }
    
    fun gregorianToHijri(gregorianDate: Date): HijriDate {
        val calendar = Calendar.getInstance()
        calendar.time = gregorianDate
        
        val gYear = calendar.get(Calendar.YEAR)
        val gMonth = calendar.get(Calendar.MONTH) + 1
        val gDay = calendar.get(Calendar.DAY_OF_MONTH)
        
        // Using Umm al-Qura calendar conversion algorithm
        val jd = gregorianToJulianDay(gYear, gMonth, gDay)
        val hijriData = julianDayToHijri(jd)
        
        val dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK)
        val dayName = arabicDayNames[(dayOfWeek - 1) % 7]
        
        return HijriDate(
            day = hijriData.first,
            month = hijriData.second,
            year = hijriData.third,
            monthName = hijriMonthNames[hijriData.second - 1],
            dayName = dayName
        )
    }
    
    fun hijriToGregorian(hijriDate: HijriDate): Date {
        val jd = hijriToJulianDay(hijriDate.year, hijriDate.month, hijriDate.day)
        return julianDayToGregorian(jd)
    }
    
    private fun gregorianToJulianDay(year: Int, month: Int, day: Int): Double {
        var y = year
        var m = month
        
        if (m <= 2) {
            y -= 1
            m += 12
        }
        
        val a = floor(y / 100.0).toInt()
        val b = 2 - a + floor(a / 4.0).toInt()
        
        return floor(365.25 * (y + 4716)) + floor(30.6001 * (m + 1)) + day + b - 1524.5
    }
    
    private fun julianDayToGregorian(jd: Double): Date {
        val z = floor(jd + 0.5).toInt()
        val a = if (z < 2299161) z else {
            val alpha = floor((z - 1867216.25) / 36524.25).toInt()
            z + 1 + alpha - floor(alpha / 4.0).toInt()
        }
        
        val b = a + 1524
        val c = floor((b - 122.1) / 365.25).toInt()
        val d = floor(365.25 * c).toInt()
        val e = floor((b - d) / 30.6001).toInt()
        
        val day = b - d - floor(30.6001 * e).toInt()
        val month = if (e < 14) e - 1 else e - 13
        val year = if (month > 2) c - 4716 else c - 4715
        
        val calendar = Calendar.getInstance()
        calendar.set(year, month - 1, day, 0, 0, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        
        return calendar.time
    }
    
    private fun julianDayToHijri(jd: Double): Triple<Int, Int, Int> {
        // Simplified Hijri conversion - for production use a more accurate library
        val epoch = 1948439.5 // Hijri epoch in Julian days
        val daysSinceEpoch = jd - epoch
        
        // Average Hijri year is about 354.367 days
        val hijriYear = floor(daysSinceEpoch / 354.367).toInt() + 1
        
        // Calculate start of Hijri year
        val yearStart = hijriToJulianDay(hijriYear, 1, 1)
        val dayOfYear = (jd - yearStart).toInt() + 1
        
        // Approximate month calculation
        val month = minOf(12, maxOf(1, floor(dayOfYear / 29.5).toInt() + 1))
        val monthStart = hijriToJulianDay(hijriYear, month, 1)
        val day = (jd - monthStart).toInt() + 1
        
        return Triple(maxOf(1, day), month, hijriYear)
    }
    
    private fun hijriToJulianDay(year: Int, month: Int, day: Int): Double {
        // Simplified conversion - for production use a more accurate library
        val epoch = 1948439.5
        val totalDays = (year - 1) * 354.367 + (month - 1) * 29.5 + day - 1
        return epoch + totalDays
    }
    
    fun getHijriMonthName(month: Int): String {
        return if (month in 1..12) hijriMonthNames[month - 1] else ""
    }
    
    fun getArabicDayName(dayOfWeek: Int): String {
        return if (dayOfWeek in 1..7) arabicDayNames[dayOfWeek - 1] else ""
    }
    
    fun isRamadan(hijriDate: HijriDate): Boolean {
        return hijriDate.month == 9
    }
    
    fun isHajjSeason(hijriDate: HijriDate): Boolean {
        return hijriDate.month == 12 && hijriDate.day in 8..13
    }
    
    fun getIslamicEvent(hijriDate: HijriDate): String? {
        return when {
            hijriDate.month == 1 && hijriDate.day == 1 -> "رأس السنة الهجرية"
            hijriDate.month == 1 && hijriDate.day == 10 -> "يوم عاشوراء"
            hijriDate.month == 3 && hijriDate.day == 12 -> "المولد النبوي الشريف"
            hijriDate.month == 7 && hijriDate.day == 27 -> "ليلة الإسراء والمعراج"
            hijriDate.month == 8 && hijriDate.day == 15 -> "ليلة البراءة"
            hijriDate.month == 9 && hijriDate.day == 1 -> "بداية شهر رمضان"
            hijriDate.month == 9 && hijriDate.day in 21..29 -> "ليلة القدر (العشر الأواخر)"
            hijriDate.month == 10 && hijriDate.day == 1 -> "عيد الفطر المبارك"
            hijriDate.month == 12 && hijriDate.day == 10 -> "عيد الأضحى المبارك"
            hijriDate.month == 12 && hijriDate.day in 8..13 -> "أيام الحج"
            else -> null
        }
    }
    
    fun formatHijriDate(hijriDate: HijriDate, includeDay: Boolean = true): String {
        return if (includeDay) {
            "${hijriDate.dayName}، ${hijriDate.day} ${hijriDate.monthName} ${hijriDate.year} هـ"
        } else {
            "${hijriDate.day} ${hijriDate.monthName} ${hijriDate.year} هـ"
        }
    }
    
    fun formatGregorianDate(gregorianDate: GregorianDate, includeDay: Boolean = true): String {
        return if (includeDay) {
            "${gregorianDate.dayName}، ${gregorianDate.day} ${gregorianDate.monthName} ${gregorianDate.year} م"
        } else {
            "${gregorianDate.day} ${gregorianDate.monthName} ${gregorianDate.year} م"
        }
    }
}
