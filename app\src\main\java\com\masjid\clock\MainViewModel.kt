
package com.masjid.clock

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale
import java.util.Date

import android.app.Application
import androidx.lifecycle.AndroidViewModel

import com.masjid.clock.models.*
import com.masjid.clock.managers.PrayerTimeManager
import com.masjid.clock.managers.HijriCalendarManager

class MainViewModel(application: Application) : AndroidViewModel(application) {

    private val settingsRepository = SettingsRepository(application)
    private val prayerTimeManager = PrayerTimeManager()
    private val hijriCalendarManager = HijriCalendarManager()

    // Basic settings
    val mosqueName = settingsRepository.mosqueNameFlow.stateIn(viewModelScope, SharingStarted.Eagerly, "مسجد السلام")
    val announcement = settingsRepository.announcementFlow.stateIn(viewModelScope, SharingStarted.Eagerly, "أهلاً وسهلاً بكم")

    // Theme and display settings
    val backgroundColor = settingsRepository.backgroundColorFlow.stateIn(viewModelScope, SharingStarted.Eagerly, "#1A1A2E")
    val textColor = settingsRepository.textColorFlow.stateIn(viewModelScope, SharingStarted.Eagerly, "#FFFFFF")
    val primaryColor = settingsRepository.primaryColorFlow.stateIn(viewModelScope, SharingStarted.Eagerly, "#6200EE")
    val secondaryColor = settingsRepository.secondaryColorFlow.stateIn(viewModelScope, SharingStarted.Eagerly, "#03DAC6")
    val accentColor = settingsRepository.accentColorFlow.stateIn(viewModelScope, SharingStarted.Eagerly, "#BB86FC")

    val fontSize = settingsRepository.fontSizeFlow.stateIn(viewModelScope, SharingStarted.Eagerly, "20")
    val fontFamily = settingsRepository.fontFamilyFlow.stateIn(viewModelScope, SharingStarted.Eagerly, "default")
    val is24HourFormat = settingsRepository.is24HourFormatFlow.stateIn(viewModelScope, SharingStarted.Eagerly, false)

    // Date display settings
    val showHijriDate = settingsRepository.showHijriDateFlow.stateIn(viewModelScope, SharingStarted.Eagerly, true)
    val showGregorianDate = settingsRepository.showGregorianDateFlow.stateIn(viewModelScope, SharingStarted.Eagerly, true)

    // Prayer and location settings
    val iqamaTimes = settingsRepository.iqamaTimesFlow.stateIn(viewModelScope, SharingStarted.Eagerly, "10,10,10,10,10")
    val locationLatitude = settingsRepository.locationLatitudeFlow.stateIn(viewModelScope, SharingStarted.Eagerly, 21.3891)
    val locationLongitude = settingsRepository.locationLongitudeFlow.stateIn(viewModelScope, SharingStarted.Eagerly, 39.8579)
    val locationName = settingsRepository.locationNameFlow.stateIn(viewModelScope, SharingStarted.Eagerly, "مكة المكرمة")

    // Audio settings
    val adhanEnabled = settingsRepository.adhanEnabledFlow.stateIn(viewModelScope, SharingStarted.Eagerly, true)
    val adhanVolume = settingsRepository.adhanVolumeFlow.stateIn(viewModelScope, SharingStarted.Eagerly, 0.8f)

    // Background and theme
    val backgroundImageUri = settingsRepository.backgroundImageUriFlow.stateIn(viewModelScope, SharingStarted.Eagerly, "")

    // State flows for UI
    private val _currentTime = MutableStateFlow("")
    val currentTime: StateFlow<String> = _currentTime

    private val _currentDate = MutableStateFlow("")
    val currentDate: StateFlow<String> = _currentDate

    private val _hijriDate = MutableStateFlow("")
    val hijriDate: StateFlow<String> = _hijriDate

    private val _dailyPrayerTimes = MutableStateFlow<DailyPrayerTimes?>(null)
    val dailyPrayerTimes: StateFlow<DailyPrayerTimes?> = _dailyPrayerTimes

    private val _nextPrayerCountdown = MutableStateFlow("")
    val nextPrayerCountdown: StateFlow<String> = _nextPrayerCountdown

    private val _iqamaCountdown = MutableStateFlow("")
    val iqamaCountdown: StateFlow<String> = _iqamaCountdown

    private val _currentPrayerStatus = MutableStateFlow("")
    val currentPrayerStatus: StateFlow<String> = _currentPrayerStatus

    private val _weather = MutableStateFlow<WeatherData?>(null)
    val weather: StateFlow<WeatherData?> = _weather

    init {
        startTimeUpdates()
        updatePrayerTimes()
    }

    private fun startTimeUpdates() {
        viewModelScope.launch {
            while (true) {
                updateCurrentTime()
                updateDates()
                updatePrayerCountdowns()
                delay(1000)
            }
        }
    }

    private fun updateCurrentTime() {
        val calendar = Calendar.getInstance()
        val timeFormat = if (is24HourFormat.value) {
            SimpleDateFormat("HH:mm:ss", Locale.getDefault())
        } else {
            SimpleDateFormat("hh:mm:ss a", Locale.getDefault())
        }
        _currentTime.value = timeFormat.format(calendar.time)
    }

    private fun updateDates() {
        val calendar = Calendar.getInstance()

        // Update Gregorian date
        if (showGregorianDate.value) {
            val gregorianDate = hijriCalendarManager.getCurrentGregorianDate()
            _currentDate.value = hijriCalendarManager.formatGregorianDate(gregorianDate)
        }

        // Update Hijri date
        if (showHijriDate.value) {
            val hijriDate = hijriCalendarManager.getCurrentHijriDate()
            _hijriDate.value = hijriCalendarManager.formatHijriDate(hijriDate)
        }
    }

    private fun updatePrayerTimes() {
        viewModelScope.launch {
            val location = LocationData(
                latitude = locationLatitude.value,
                longitude = locationLongitude.value,
                name = locationName.value
            )

            val iqamaTimesList = iqamaTimes.value.split(",").map { it.toIntOrNull() ?: 10 }
            val iqamaSettings = IqamaSettings(
                fajr = iqamaTimesList.getOrNull(0) ?: 10,
                dhuhr = iqamaTimesList.getOrNull(1) ?: 10,
                asr = iqamaTimesList.getOrNull(2) ?: 10,
                maghrib = iqamaTimesList.getOrNull(3) ?: 5,
                isha = iqamaTimesList.getOrNull(4) ?: 10
            )

            val dailyPrayers = prayerTimeManager.calculatePrayerTimes(
                location = location,
                iqamaSettings = iqamaSettings
            )

            _dailyPrayerTimes.value = dailyPrayers
        }
    }

    private fun updatePrayerCountdowns() {
        dailyPrayerTimes.value?.let { prayers ->
            // Update next prayer countdown
            val timeUntilNext = prayerTimeManager.getTimeUntilNextPrayer(prayers)
            _nextPrayerCountdown.value = prayerTimeManager.formatTimeRemaining(timeUntilNext)

            // Update iqama countdown
            val timeUntilIqama = prayerTimeManager.getTimeUntilIqama(prayers)
            _iqamaCountdown.value = if (timeUntilIqama != null) {
                prayerTimeManager.formatTimeRemaining(timeUntilIqama)
            } else ""

            // Update prayer status
            _currentPrayerStatus.value = prayerTimeManager.getCurrentPrayerStatus(prayers)
        }
    }

    fun updateLocation(latitude: Double, longitude: Double, name: String = "") {
        viewModelScope.launch {
            settingsRepository.saveLocationLatitude(latitude)
            settingsRepository.saveLocationLongitude(longitude)
            if (name.isNotEmpty()) {
                settingsRepository.saveLocationName(name)
            }
            updatePrayerTimes()
        }
    }

    // Save functions for settings
    fun saveMosqueName(name: String) {
        viewModelScope.launch {
            settingsRepository.saveMosqueName(name)
        }
    }

    fun saveAnnouncement(announcement: String) {
        viewModelScope.launch {
            settingsRepository.saveAnnouncement(announcement)
        }
    }

    fun saveBackgroundColor(color: String) {
        viewModelScope.launch {
            settingsRepository.saveBackgroundColor(color)
        }
    }

    fun saveTextColor(color: String) {
        viewModelScope.launch {
            settingsRepository.saveTextColor(color)
        }
    }

    fun saveIqamaTimes(times: String) {
        viewModelScope.launch {
            settingsRepository.saveIqamaTimes(times)
            updatePrayerTimes()
        }
    }

    fun saveFontSize(size: String) {
        viewModelScope.launch {
            settingsRepository.saveFontSize(size)
        }
    }

    fun saveBackgroundImageUri(uri: String) {
        viewModelScope.launch {
            settingsRepository.saveBackgroundImageUri(uri)
        }
    }

    fun saveIs24HourFormat(is24Hour: Boolean) {
        viewModelScope.launch {
            settingsRepository.saveIs24HourFormat(is24Hour)
        }
    }

    fun saveShowHijriDate(show: Boolean) {
        viewModelScope.launch {
            settingsRepository.saveShowHijriDate(show)
        }
    }

    fun saveShowGregorianDate(show: Boolean) {
        viewModelScope.launch {
            settingsRepository.saveShowGregorianDate(show)
        }
    }

    fun saveAdhanEnabled(enabled: Boolean) {
        viewModelScope.launch {
            settingsRepository.saveAdhanEnabled(enabled)
        }
    }

    fun saveAdhanVolume(volume: Float) {
        viewModelScope.launch {
            settingsRepository.saveAdhanVolume(volume)
        }
    }
}

