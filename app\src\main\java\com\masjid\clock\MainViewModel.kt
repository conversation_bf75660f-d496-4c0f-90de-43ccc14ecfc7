
package com.masjid.clock

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale

import com.batoulapps.adhan.CalculationMethod
import com.batoulapps.adhan.Coordinates
import com.batoulapps.adhan.Madhab
import com.batoulapps.adhan.PrayerTimes
import com.batoulapps.adhan.data.DateComponents
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale


import android.app.Application
import androidx.lifecycle.AndroidViewModel

class MainViewModel(application: Application) : AndroidViewModel(application) {

    private val settingsRepository = SettingsRepository(application)

    val mosqueName = settingsRepository.mosqueNameFlow.stateIn(viewModelScope, SharingStarted.Eagerly, "My Mosque")
    val announcement = settingsRepository.announcementFlow.stateIn(viewModelScope, SharingStarted.Eagerly, "Welcome")

    val backgroundColor = settingsRepository.backgroundColorFlow.stateIn(viewModelScope, SharingStarted.Eagerly, "#FFFFFF")
    val textColor = settingsRepository.textColorFlow.stateIn(viewModelScope, SharingStarted.Eagerly, "#000000")

    val iqamaTimes = settingsRepository.iqamaTimesFlow.stateIn(viewModelScope, SharingStarted.Eagerly, "10,10,10,10,10")

    val backgroundImageUri = settingsRepository.backgroundImageUriFlow.stateIn(viewModelScope, SharingStarted.Eagerly, "")

    val fontSize = settingsRepository.fontSizeFlow.stateIn(viewModelScope, SharingStarted.Eagerly, "20")

    private val _currentTime = MutableStateFlow("")
    val currentTime: StateFlow<String> = _currentTime

    private val _currentDate = MutableStateFlow("")
    val currentDate: StateFlow<String> = _currentDate

    private val _prayerTimes = MutableStateFlow<PrayerTimes?>(null)
    val prayerTimes: StateFlow<PrayerTimes?> = _prayerTimes

    private val _hijriDate = MutableStateFlow("")
    val hijriDate: StateFlow<String> = _hijriDate

    init {
        viewModelScope.launch {
            while (true) {
                val calendar = Calendar.getInstance()
                _currentTime.value = SimpleDateFormat("hh:mm:ss a", Locale.getDefault()).format(calendar.time)
                _currentDate.value = SimpleDateFormat("dd MMMM yyyy", Locale.getDefault()).format(calendar.time)
                // This is a simplified Hijri date calculation, a more accurate library might be needed
                _hijriDate.value = "12 Ramadan 1445 H"
                delay(1000)
            }
        }
    }

    import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory

    private val _weather = MutableStateFlow<WeatherResponse?>(null)
    val weather: StateFlow<WeatherResponse?> = _weather

    private val weatherApiService: WeatherApiService by lazy {
        Retrofit.Builder()
            .baseUrl("https://api.openweathermap.org/data/2.5/")
            .addConverterFactory(GsonConverterFactory.create())
            .build()
            .create(WeatherApiService::class.java)
    }

    fun updateLocation(latitude: Double, longitude: Double) {
        viewModelScope.launch {
            val coordinates = Coordinates(latitude, longitude)
            val date = DateComponents.from(Date())
            val params = CalculationMethod.UMM_AL_QURA.parameters
            params.madhab = Madhab.HANAFI
            val prayerTimes = PrayerTimes(coordinates, date, params)
            _prayerTimes.value = prayerTimes

            try {
                // Replace with your own API key
                val response = weatherApiService.getWeather(latitude, longitude, "YOUR_API_KEY")
                _weather.value = response
            } catch (e: Exception) {
                // Handle error
            }
        }
    }

    fun saveMosqueName(name: String) {
        viewModelScope.launch {
            settingsRepository.saveMosqueName(name)
        }
    }

    fun saveAnnouncement(announcement: String) {
        viewModelScope.launch {
            settingsRepository.saveAnnouncement(announcement)
        }
    }
}

