package com.masjid.clock.models

import java.util.Date

data class PrayerTimeData(
    val name: String,
    val time: Date,
    val iqamaTime: Date,
    val adjustment: Int = 0, // minutes
    val isNext: Boolean = false,
    val isPassed: Boolean = false
)

data class DailyPrayerTimes(
    val fajr: PrayerTimeData,
    val sunrise: Date,
    val dhuhr: PrayerTimeData,
    val asr: PrayerTimeData,
    val maghrib: PrayerTimeData,
    val isha: PrayerTimeData,
    val date: Date
) {
    fun getAllPrayers(): List<PrayerTimeData> {
        return listOf(fajr, dhuhr, asr, maghrib, isha)
    }
    
    fun getNextPrayer(): PrayerTimeData? {
        val now = System.currentTimeMillis()
        return getAllPrayers().firstOrNull { it.time.time > now }
    }
    
    fun getCurrentOrNextPrayer(): PrayerTimeData? {
        val now = System.currentTimeMillis()
        val currentPrayer = getAllPrayers().lastOrNull { it.time.time <= now }
        val nextPrayer = getNextPrayer()
        
        // If we're within iqama time of current prayer, return current
        currentPrayer?.let { current ->
            if (now <= current.iqamaTime.time) {
                return current
            }
        }
        
        return nextPrayer
    }
}

data class LocationData(
    val latitude: Double,
    val longitude: Double,
    val name: String,
    val country: String = "",
    val city: String = ""
)

data class HijriDate(
    val day: Int,
    val month: Int,
    val year: Int,
    val monthName: String,
    val dayName: String = ""
) {
    fun getFormattedDate(): String {
        return "$day $monthName $year هـ"
    }
}

data class GregorianDate(
    val day: Int,
    val month: Int,
    val year: Int,
    val monthName: String,
    val dayName: String
) {
    fun getFormattedDate(): String {
        return "$dayName، $day $monthName $year"
    }
}

data class WeatherData(
    val temperature: Double,
    val description: String,
    val humidity: Int,
    val windSpeed: Double,
    val icon: String
)

data class ThemeColors(
    val primary: String = "#6200EE",
    val secondary: String = "#03DAC6",
    val accent: String = "#BB86FC",
    val background: String = "#FFFFFF",
    val surface: String = "#FFFFFF",
    val onPrimary: String = "#FFFFFF",
    val onSecondary: String = "#000000",
    val onBackground: String = "#000000",
    val onSurface: String = "#000000",
    val text: String = "#000000"
)

data class GradientColors(
    val startColor: String,
    val endColor: String,
    val enabled: Boolean = false
)

enum class OrientationMode {
    AUTO, PORTRAIT, LANDSCAPE
}

enum class ThemeMode {
    LIGHT, DARK, AUTO
}

enum class CalculationMethodType {
    UMM_AL_QURA,
    EGYPTIAN,
    KARACHI,
    NORTH_AMERICA,
    MUSLIM_WORLD_LEAGUE,
    DUBAI,
    MOON_SIGHTING_COMMITTEE,
    SINGAPORE,
    TURKEY,
    TEHRAN,
    CUSTOM
}

enum class MadhabType {
    SHAFI,
    HANAFI
}

data class PrayerAdjustments(
    val fajr: Int = 0,
    val dhuhr: Int = 0,
    val asr: Int = 0,
    val maghrib: Int = 0,
    val isha: Int = 0
)

data class IqamaSettings(
    val fajr: Int = 10,
    val dhuhr: Int = 10,
    val asr: Int = 10,
    val maghrib: Int = 5,
    val isha: Int = 10,
    val showCountdown: Boolean = true,
    val countdownSize: Float = 1.5f
)

data class DisplaySettings(
    val fontSize: Float = 20f,
    val fontFamily: String = "default",
    val is24HourFormat: Boolean = false,
    val showHijriDate: Boolean = true,
    val showGregorianDate: Boolean = true,
    val orientationMode: OrientationMode = OrientationMode.AUTO,
    val screenDimmingTime: Int = 30, // minutes
    val autoHideScreen: Boolean = false
)

data class AudioSettings(
    val adhanEnabled: Boolean = true,
    val adhanVolume: Float = 0.8f,
    val customAdhanUri: String = "",
    val customSoundsEnabled: Boolean = false
)

data class MosqueInfo(
    val name: String = "مسجد السلام",
    val announcement: String = "أهلاً وسهلاً بكم في مسجد السلام",
    val location: LocationData? = null
)
