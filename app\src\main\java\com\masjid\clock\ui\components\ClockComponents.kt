package com.masjid.clock.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.masjid.clock.models.DailyPrayerTimes
import com.masjid.clock.models.PrayerTimeData
import com.masjid.clock.models.WeatherData
import java.text.SimpleDateFormat
import java.util.*

@Composable
fun MosqueHeader(
    mosqueName: String,
    textColor: String,
    fontSize: String
) {
    Text(
        text = mosqueName,
        fontSize = (fontSize.toIntOrNull()?.plus(8) ?: 28).sp,
        fontWeight = FontWeight.Bold,
        color = Color(android.graphics.Color.parseColor(textColor)),
        textAlign = TextAlign.Center,
        modifier = Modifier.fillMaxWidth()
    )
}

@Composable
fun MainClockSection(
    currentTime: String,
    currentDate: String,
    hijriDate: String,
    textColor: String,
    fontSize: String,
    showHijriDate: Boolean,
    showGregorianDate: Boolean,
    weather: WeatherData?
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // Main time display
        Text(
            text = currentTime,
            fontSize = (fontSize.toIntOrNull()?.plus(32) ?: 52).sp,
            fontWeight = FontWeight.Bold,
            color = Color(android.graphics.Color.parseColor(textColor)),
            textAlign = TextAlign.Center
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // Dates
        if (showGregorianDate) {
            Text(
                text = currentDate,
                fontSize = (fontSize.toIntOrNull()?.plus(2) ?: 22).sp,
                color = Color(android.graphics.Color.parseColor(textColor)).copy(alpha = 0.9f),
                textAlign = TextAlign.Center
            )
        }
        
        if (showHijriDate) {
            Spacer(modifier = Modifier.height(4.dp))
            Text(
                text = hijriDate,
                fontSize = (fontSize.toIntOrNull()?.plus(2) ?: 22).sp,
                color = Color(android.graphics.Color.parseColor(textColor)).copy(alpha = 0.9f),
                textAlign = TextAlign.Center
            )
        }
        
        // Weather info
        weather?.let {
            Spacer(modifier = Modifier.height(8.dp))
            WeatherSection(
                weather = it,
                textColor = textColor,
                fontSize = fontSize
            )
        }
    }
}

@Composable
fun PrayerTimesGrid(
    prayerTimes: DailyPrayerTimes,
    textColor: String,
    primaryColor: String,
    fontSize: String,
    isLandscape: Boolean
) {
    val prayers = prayerTimes.getAllPrayers()
    val timeFormatter = SimpleDateFormat("HH:mm", Locale.getDefault())
    
    if (isLandscape) {
        // Vertical layout for landscape
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            prayers.forEach { prayer ->
                PrayerTimeCard(
                    prayer = prayer,
                    textColor = textColor,
                    primaryColor = primaryColor,
                    fontSize = fontSize,
                    timeFormatter = timeFormatter,
                    isCompact = true
                )
            }
        }
    } else {
        // Grid layout for portrait
        Column(
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // First row: Fajr, Dhuhr, Asr
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                prayers.take(3).forEach { prayer ->
                    PrayerTimeCard(
                        prayer = prayer,
                        textColor = textColor,
                        primaryColor = primaryColor,
                        fontSize = fontSize,
                        timeFormatter = timeFormatter,
                        modifier = Modifier.weight(1f)
                    )
                }
            }
            
            // Second row: Maghrib, Isha
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                prayers.drop(3).forEach { prayer ->
                    PrayerTimeCard(
                        prayer = prayer,
                        textColor = textColor,
                        primaryColor = primaryColor,
                        fontSize = fontSize,
                        timeFormatter = timeFormatter,
                        modifier = Modifier.weight(1f)
                    )
                }
                // Empty space to balance the row
                Spacer(modifier = Modifier.weight(1f))
            }
        }
    }
}

@Composable
fun PrayerTimeCard(
    prayer: PrayerTimeData,
    textColor: String,
    primaryColor: String,
    fontSize: String,
    timeFormatter: SimpleDateFormat,
    modifier: Modifier = Modifier,
    isCompact: Boolean = false
) {
    val cardColor = if (prayer.isNext) {
        Color(android.graphics.Color.parseColor(primaryColor)).copy(alpha = 0.8f)
    } else {
        Color(android.graphics.Color.parseColor(textColor)).copy(alpha = 0.1f)
    }
    
    val contentColor = if (prayer.isNext) {
        Color.White
    } else {
        Color(android.graphics.Color.parseColor(textColor))
    }
    
    Card(
        modifier = modifier
            .padding(4.dp)
            .clip(RoundedCornerShape(12.dp)),
        colors = CardDefaults.cardColors(containerColor = cardColor),
        elevation = CardDefaults.cardElevation(defaultElevation = if (prayer.isNext) 8.dp else 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(if (isCompact) 8.dp else 16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = prayer.name,
                fontSize = (fontSize.toIntOrNull()?.plus(if (isCompact) 0 else 2) ?: if (isCompact) 16 else 18).sp,
                fontWeight = FontWeight.Bold,
                color = contentColor,
                textAlign = TextAlign.Center
            )
            
            Spacer(modifier = Modifier.height(4.dp))
            
            Text(
                text = timeFormatter.format(prayer.time),
                fontSize = (fontSize.toIntOrNull()?.plus(if (isCompact) -2 else 0) ?: if (isCompact) 14 else 16).sp,
                color = contentColor.copy(alpha = 0.9f),
                textAlign = TextAlign.Center
            )
            
            if (prayer.adjustment != 0) {
                Text(
                    text = if (prayer.adjustment > 0) "+${prayer.adjustment}" else "${prayer.adjustment}",
                    fontSize = (fontSize.toIntOrNull()?.minus(4) ?: 12).sp,
                    color = contentColor.copy(alpha = 0.7f),
                    textAlign = TextAlign.Center
                )
            }
        }
    }
}

@Composable
fun IqamaCountdownSection(
    countdown: String,
    prayerStatus: String,
    textColor: String,
    fontSize: String
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(8.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.Red.copy(alpha = 0.8f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = prayerStatus,
                fontSize = (fontSize.toIntOrNull()?.plus(4) ?: 24).sp,
                fontWeight = FontWeight.Bold,
                color = Color.White,
                textAlign = TextAlign.Center
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "الإقامة خلال: $countdown",
                fontSize = (fontSize.toIntOrNull()?.plus(8) ?: 28).sp,
                fontWeight = FontWeight.Bold,
                color = Color.White,
                textAlign = TextAlign.Center
            )
        }
    }
}

@Composable
fun WeatherSection(
    weather: WeatherData,
    textColor: String,
    fontSize: String
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = "${weather.temperature.toInt()}°",
            fontSize = (fontSize.toIntOrNull()?.plus(4) ?: 24).sp,
            fontWeight = FontWeight.Bold,
            color = Color(android.graphics.Color.parseColor(textColor))
        )
        
        Text(
            text = weather.description,
            fontSize = (fontSize.toIntOrNull()?.minus(2) ?: 14).sp,
            color = Color(android.graphics.Color.parseColor(textColor)).copy(alpha = 0.8f)
        )
    }
}

@Composable
fun AnnouncementSection(
    announcement: String,
    textColor: String,
    fontSize: String
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(8.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color(android.graphics.Color.parseColor(textColor)).copy(alpha = 0.1f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Text(
            text = announcement,
            fontSize = (fontSize.toIntOrNull() ?: 16).sp,
            color = Color(android.graphics.Color.parseColor(textColor)),
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(16.dp)
        )
    }
}
