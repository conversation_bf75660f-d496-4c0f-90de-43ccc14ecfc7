{"logs": [{"outputFile": "com.masjid.clock.app-mergeDebugResources-55:/values-gl/values-gl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\39edba29d1252d0b67d5e84f709fb54f\\transformed\\appcompat-1.6.1\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,313,421,506,607,735,821,902,994,1088,1185,1279,1379,1473,1569,1664,1756,1848,1929,2037,2144,2251,2360,2465,2579,2756,2855", "endColumns": "103,103,107,84,100,127,85,80,91,93,96,93,99,93,95,94,91,91,80,107,106,106,108,104,113,176,98,82", "endOffsets": "204,308,416,501,602,730,816,897,989,1083,1180,1274,1374,1468,1564,1659,1751,1843,1924,2032,2139,2246,2355,2460,2574,2751,2850,2933"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "325,429,533,641,726,827,955,1041,1122,1214,1308,1405,1499,1599,1693,1789,1884,1976,2068,2149,2257,2364,2471,2580,2685,2799,2976,9362", "endColumns": "103,103,107,84,100,127,85,80,91,93,96,93,99,93,95,94,91,91,80,107,106,106,108,104,113,176,98,82", "endOffsets": "424,528,636,721,822,950,1036,1117,1209,1303,1400,1494,1594,1688,1784,1879,1971,2063,2144,2252,2359,2466,2575,2680,2794,2971,3070,9440"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\9fd9d7009dd2359dbdb604cdcc345b0a\\transformed\\jetified-ui-1.4.0\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,284,393,494,583,662,755,847,935,1008,1078,1163,1253,1330,1412,1484", "endColumns": "95,82,108,100,88,78,92,91,87,72,69,84,89,76,81,71,121", "endOffsets": "196,279,388,489,578,657,750,842,930,1003,1073,1158,1248,1325,1407,1479,1601"}, "to": {"startLines": "41,42,43,44,45,49,50,101,102,103,104,106,107,109,111,112,113", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3837,3933,4016,4125,4226,4568,4647,8779,8871,8959,9032,9187,9272,9445,9623,9705,9777", "endColumns": "95,82,108,100,88,78,92,91,87,72,69,84,89,76,81,71,121", "endOffsets": "3928,4011,4120,4221,4310,4642,4735,8866,8954,9027,9097,9267,9357,9517,9700,9772,9894"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\f7c8c22bdafe0bf1b7b8f05029141cd9\\transformed\\core-1.9.0\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "110", "startColumns": "4", "startOffsets": "9522", "endColumns": "100", "endOffsets": "9618"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\6afa17b2b3e5aa97934ff0de89083c5d\\transformed\\material-1.8.0\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,275,356,436,521,623,719,824,957,1037,1115,1211,1290,1353,1448,1512,1581,1644,1718,1782,1838,1959,2017,2079,2135,2212,2351,2439,2519,2659,2739,2819,2909,2965,3021,3087,3166,3247,3335,3414,3491,3573,3662,3746,3838,3931,4032,4106,4198,4300,4352,4418,4510,4598,4660,4724,4787,4898,5000,5106,5209,5269,5329", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,80,79,84,101,95,104,132,79,77,95,78,62,94,63,68,62,73,63,55,120,57,61,55,76,138,87,79,139,79,79,89,55,55,65,78,80,87,78,76,81,88,83,91,92,100,73,91,101,51,65,91,87,61,63,62,110,101,105,102,59,59,84", "endOffsets": "270,351,431,516,618,714,819,952,1032,1110,1206,1285,1348,1443,1507,1576,1639,1713,1777,1833,1954,2012,2074,2130,2207,2346,2434,2514,2654,2734,2814,2904,2960,3016,3082,3161,3242,3330,3409,3486,3568,3657,3741,3833,3926,4027,4101,4193,4295,4347,4413,4505,4593,4655,4719,4782,4893,4995,5101,5204,5264,5324,5409"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,46,47,48,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,105", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3075,3156,3236,3321,3423,3519,3624,3757,4315,4393,4489,4740,4803,4898,4962,5031,5094,5168,5232,5288,5409,5467,5529,5585,5662,5801,5889,5969,6109,6189,6269,6359,6415,6471,6537,6616,6697,6785,6864,6941,7023,7112,7196,7288,7381,7482,7556,7648,7750,7802,7868,7960,8048,8110,8174,8237,8348,8450,8556,8659,8719,9102", "endLines": "5,33,34,35,36,37,38,39,40,46,47,48,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,105", "endColumns": "12,80,79,84,101,95,104,132,79,77,95,78,62,94,63,68,62,73,63,55,120,57,61,55,76,138,87,79,139,79,79,89,55,55,65,78,80,87,78,76,81,88,83,91,92,100,73,91,101,51,65,91,87,61,63,62,110,101,105,102,59,59,84", "endOffsets": "320,3151,3231,3316,3418,3514,3619,3752,3832,4388,4484,4563,4798,4893,4957,5026,5089,5163,5227,5283,5404,5462,5524,5580,5657,5796,5884,5964,6104,6184,6264,6354,6410,6466,6532,6611,6692,6780,6859,6936,7018,7107,7191,7283,7376,7477,7551,7643,7745,7797,7863,7955,8043,8105,8169,8232,8343,8445,8551,8654,8714,8774,9182"}}]}]}