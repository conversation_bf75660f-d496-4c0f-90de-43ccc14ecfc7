package com.masjid.clock

import android.net.Uri
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.*
import androidx.compose.material.Button
import androidx.compose.material.Text
import androidx.compose.material.TextField
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import coil.compose.rememberImagePainter

@Composable
fun SettingsScreen(viewModel: MainViewModel = viewModel()) {
    val mosqueName by viewModel.mosqueName.collectAsState()
    val announcement by viewModel.announcement.collectAsState()
    val backgroundColor by viewModel.backgroundColor.collectAsState()
    val textColor by viewModel.textColor.collectAsState()
    val iqamaTimes by viewModel.iqamaTimes.collectAsState()
    val fontSize by viewModel.fontSize.collectAsState()

    var tempMosqueName by remember { mutableStateOf(mosqueName) }
    var tempAnnouncement by remember { mutableStateOf(announcement) }
    var tempBackgroundColor by remember { mutableStateOf(backgroundColor) }
    var tempTextColor by remember { mutableStateOf(textColor) }
    var tempIqamaTimes by remember { mutableStateOf(iqamaTimes) }
    var tempFontSize by remember { mutableStateOf(fontSize) }
    var imageUri by remember { mutableStateOf<Uri?>(null) }

    val launcher = rememberLauncherForActivityResult(contract = ActivityResultContracts.GetContent()) { uri: Uri? ->
        imageUri = uri
    }

    Column(modifier = Modifier.padding(16.dp)) {
        TextField(
            value = tempMosqueName,
            onValueChange = { tempMosqueName = it },
            label = { Text("Mosque Name") },
            modifier = Modifier.fillMaxWidth()
        )
        Spacer(modifier = Modifier.height(16.dp))
        TextField(
            value = tempAnnouncement,
            onValueChange = { tempAnnouncement = it },
            label = { Text("Announcement") },
            modifier = Modifier.fillMaxWidth()
        )
        Spacer(modifier = Modifier.height(16.dp))
        TextField(
            value = tempBackgroundColor,
            onValueChange = { tempBackgroundColor = it },
            label = { Text("Background Color (e.g., #FFFFFF)") },
            modifier = Modifier.fillMaxWidth()
        )
        Spacer(modifier = Modifier.height(16.dp))
        TextField(
            value = tempTextColor,
            onValueChange = { tempTextColor = it },
            label = { Text("Text Color (e.g., #000000)") },
            modifier = Modifier.fillMaxWidth()
        )
        Spacer(modifier = Modifier.height(16.dp))
        TextField(
            value = tempIqamaTimes,
            onValueChange = { tempIqamaTimes = it },
            label = { Text("Iqama Times (Fajr,Dhuhr,Asr,Maghrib,Isha)") },
            modifier = Modifier.fillMaxWidth()
        )
        Spacer(modifier = Modifier.height(16.dp))
        TextField(
            value = tempFontSize,
            onValueChange = { tempFontSize = it },
            label = { Text("Font Size") },
            modifier = Modifier.fillMaxWidth()
        )
        Spacer(modifier = Modifier.height(16.dp))
        Button(onClick = { launcher.launch("image/*") }) {
            Text("Select Background Image")
        }

        imageUri?.let {
            Spacer(modifier = Modifier.height(16.dp))
            Image(
                painter = rememberImagePainter(it),
                contentDescription = null,
                modifier = Modifier.fillMaxWidth(),
                contentScale = ContentScale.Crop
            )
        }

        Spacer(modifier = Modifier.height(16.dp))
        Button(onClick = {
            viewModel.saveMosqueName(tempMosqueName)
            viewModel.saveAnnouncement(tempAnnouncement)
            viewModel.saveBackgroundColor(tempBackgroundColor)
            viewModel.saveTextColor(tempTextColor)
            viewModel.saveIqamaTimes(tempIqamaTimes)
            viewModel.saveFontSize(tempFontSize)
            imageUri?.let { viewModel.saveBackgroundImageUri(it.toString()) }
        }) {
            Text("Save")
        }
    }
}