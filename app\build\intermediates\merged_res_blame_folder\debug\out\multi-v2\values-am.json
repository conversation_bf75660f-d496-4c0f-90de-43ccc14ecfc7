{"logs": [{"outputFile": "com.masjid.clock.app-mergeDebugResources-55:/values-am/values-am.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\f7c8c22bdafe0bf1b7b8f05029141cd9\\transformed\\core-1.9.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "110", "startColumns": "4", "startOffsets": "8675", "endColumns": "100", "endOffsets": "8771"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\39edba29d1252d0b67d5e84f709fb54f\\transformed\\appcompat-1.6.1\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,203,301,407,493,596,713,791,867,958,1051,1143,1237,1337,1430,1525,1618,1709,1800,1880,1980,2080,2176,2278,2378,2477,2627,2723", "endColumns": "97,97,105,85,102,116,77,75,90,92,91,93,99,92,94,92,90,90,79,99,99,95,101,99,98,149,95,79", "endOffsets": "198,296,402,488,591,708,786,862,953,1046,1138,1232,1332,1425,1520,1613,1704,1795,1875,1975,2075,2171,2273,2373,2472,2622,2718,2798"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "311,409,507,613,699,802,919,997,1073,1164,1257,1349,1443,1543,1636,1731,1824,1915,2006,2086,2186,2286,2382,2484,2584,2683,2833,8525", "endColumns": "97,97,105,85,102,116,77,75,90,92,91,93,99,92,94,92,90,90,79,99,99,95,101,99,98,149,95,79", "endOffsets": "404,502,608,694,797,914,992,1068,1159,1252,1344,1438,1538,1631,1726,1819,1910,2001,2081,2181,2281,2377,2479,2579,2678,2828,2924,8600"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\9fd9d7009dd2359dbdb604cdcc345b0a\\transformed\\jetified-ui-1.4.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,188,265,357,453,535,613,696,778,856,922,988,1066,1147,1217,1297,1362", "endColumns": "82,76,91,95,81,77,82,81,77,65,65,77,80,69,79,64,115", "endOffsets": "183,260,352,448,530,608,691,773,851,917,983,1061,1142,1212,1292,1357,1473"}, "to": {"startLines": "41,42,43,44,45,49,50,101,102,103,104,106,107,109,111,112,113", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3574,3657,3734,3826,3922,4215,4293,7998,8080,8158,8224,8366,8444,8605,8776,8856,8921", "endColumns": "82,76,91,95,81,77,82,81,77,65,65,77,80,69,79,64,115", "endOffsets": "3652,3729,3821,3917,3999,4288,4371,8075,8153,8219,8285,8439,8520,8670,8851,8916,9032"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\6afa17b2b3e5aa97934ff0de89083c5d\\transformed\\material-1.8.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,261,332,400,475,557,638,727,829,906,970,1055,1117,1175,1260,1323,1385,1443,1509,1571,1626,1722,1779,1838,1894,1961,2066,2146,2227,2356,2429,2500,2582,2633,2684,2750,2816,2889,2970,3038,3111,3182,3249,3334,3401,3488,3576,3650,3718,3803,3854,3918,3998,4080,4142,4206,4269,4364,4453,4538,4629,4684,4739", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,70,67,74,81,80,88,101,76,63,84,61,57,84,62,61,57,65,61,54,95,56,58,55,66,104,79,80,128,72,70,81,50,50,65,65,72,80,67,72,70,66,84,66,86,87,73,67,84,50,63,79,81,61,63,62,94,88,84,90,54,54,75", "endOffsets": "256,327,395,470,552,633,722,824,901,965,1050,1112,1170,1255,1318,1380,1438,1504,1566,1621,1717,1774,1833,1889,1956,2061,2141,2222,2351,2424,2495,2577,2628,2679,2745,2811,2884,2965,3033,3106,3177,3244,3329,3396,3483,3571,3645,3713,3798,3849,3913,3993,4075,4137,4201,4264,4359,4448,4533,4624,4679,4734,4810"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,46,47,48,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,105", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2929,3000,3068,3143,3225,3306,3395,3497,4004,4068,4153,4376,4434,4519,4582,4644,4702,4768,4830,4885,4981,5038,5097,5153,5220,5325,5405,5486,5615,5688,5759,5841,5892,5943,6009,6075,6148,6229,6297,6370,6441,6508,6593,6660,6747,6835,6909,6977,7062,7113,7177,7257,7339,7401,7465,7528,7623,7712,7797,7888,7943,8290", "endLines": "5,33,34,35,36,37,38,39,40,46,47,48,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,105", "endColumns": "12,70,67,74,81,80,88,101,76,63,84,61,57,84,62,61,57,65,61,54,95,56,58,55,66,104,79,80,128,72,70,81,50,50,65,65,72,80,67,72,70,66,84,66,86,87,73,67,84,50,63,79,81,61,63,62,94,88,84,90,54,54,75", "endOffsets": "306,2995,3063,3138,3220,3301,3390,3492,3569,4063,4148,4210,4429,4514,4577,4639,4697,4763,4825,4880,4976,5033,5092,5148,5215,5320,5400,5481,5610,5683,5754,5836,5887,5938,6004,6070,6143,6224,6292,6365,6436,6503,6588,6655,6742,6830,6904,6972,7057,7108,7172,7252,7334,7396,7460,7523,7618,7707,7792,7883,7938,7993,8361"}}]}]}