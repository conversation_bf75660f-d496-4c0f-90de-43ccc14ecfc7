#!/usr/bin/env sh

#
# Copyright 2015 the original author or authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

# Add default JVM options here. You can also use JAVA_OPTS and GRADLE_OPTS to pass JVM options to this script.
DEFAULT_JVM_OPTS=""

APP_NAME="Gradle"
APP_BASE_NAME=`basename "$0"`

# Use the maximum available, or set MAX_FD != -1 to use that value.
MAX_FD="maximum"

warn () {
    echo "$*"
}

die () {
    echo
    echo "$*"
    echo
    exit 1
}

# OS specific support (must be 'true' or 'false').
cygwin=false
msys=false
darwin=false
nonstop=false
case "`uname`" in
  CYGWIN* )
    cygwin=true
    ;;
  Darwin* )
    darwin=true
    ;;
  MINGW* )
    msys=true
    ;;
  NONSTOP* )
    nonstop=true
    ;;
esac

# Attempt to set APP_HOME
# Resolve links: $0 may be a link
PRG="$0"
# Need this for relative symlinks.
while [ -h "$PRG" ] ; do
    ls=`ls -ld "$PRG"`
    link=`expr "$ls" : '.*-> \(.*\)$'`
    if expr "$link" : '/.*' > /dev/null; then
        PRG="$link"
    else
        PRG=`dirname "$PRG"`"/$link"
    fi
done
SAVED="`pwd`"
cd "`dirname \"$PRG\"`/" >/dev/null
APP_HOME="`pwd -P`"
cd "$SAVED" >/dev/null

# Add the Wrapper to the classpath
WRAPPER_JAR="$APP_HOME/gradle/wrapper/gradle-wrapper.jar"

# Use the maximum available, or set MAX_FD != -1 to use that value.
MAX_FD="maximum"

if [ "$cygwin" = "true" -o "$msys" = "true" ] ; then
    # Add the Wrapper in path style for MinGW and Cygwin
    WRAPPER_JAR=`cygpath --path --mixed "$WRAPPER_JAR"`
fi

# Determine the Java command to use to start the JVM.
if [ -n "$JAVA_HOME" ] ; then
    if [ -x "$JAVA_HOME/jre/sh/java" ] ; then
        # IBM's JDK on AIX uses strange locations for the executables
        JAVACMD="$JAVA_HOME/jre/sh/java"
    else
        JAVACMD="$JAVA_HOME/bin/java"
    fi
    if [ ! -x "$JAVACMD" ] ; then
        die "ERROR: JAVA_HOME is set to an invalid directory: $JAVA_HOME

Please set the JAVA_HOME variable in your environment to match the
location of your Java installation."
    fi
else
    JAVACMD="java"
    which java >/dev/null 2>&1 || die "ERROR: JAVA_HOME is not set and no 'java' command could be found in your PATH.

Please set the JAVA_HOME variable in your environment to match the
location of your Java installation."
fi

# Increase the maximum file descriptors if we can.
if [ "$cygwin" = "false" -a "$darwin" = "false" -a "$nonstop" = "false" ] ; then
    MAX_FD_LIMIT=`ulimit -H -n`
    if [ $? -eq 0 ] ; then
        if [ "$MAX_FD" = "maximum" -o "$MAX_FD" = "max" ] ; then
            # Use the system limit
            MAX_FD="$MAX_FD_LIMIT"
        fi
        ulimit -n $MAX_FD
        if [ $? -ne 0 ] ; then
            warn "Could not set maximum file descriptor limit: $MAX_FD"
        fi
    else
        warn "Could not query maximum file descriptor limit: $MAX_FD_LIMIT"
    fi
fi

# For Darwin, add options to specify how the application appears in the dock
if $darwin; then
    GRADLE_OPTS="$GRADLE_OPTS \"-Xdock:name=$APP_NAME\" \"-Xdock:icon=$APP_HOME/media/gradle.icns\""
fi

# For Cygwin, switch paths to Windows format before running java
if $cygwin ; then
    APP_HOME=`cygpath --path --mixed "$APP_HOME"`
    CLASSPATH=`cygpath --path --mixed "$CLASSPATH"`
    JAVACMD=`cygpath --unix "$JAVACMD"`

    # We build the pattern for arguments to be converted to Windows paths
    ROOTDIRSRAW=`find -L / -maxdepth 1 -mindepth 1 -type d 2>/dev/null`
    SEP=""
    for dir in $ROOTDIRSRAW ; do
        ROOTDIRS="$ROOTDIRS$SEP$dir"
        SEP="|"
    done
    OURCYGPID=`$$`
    PARENTCYGPID=`ps -p $OURCYGPID -o ppid --no-headers`
    UNCAPATHS=`ps -p $PARENTCYGPID -o args --no-headers | grep -o -E 'UNC/[^ ]+'`
    if [ -n "$UNCAPATHS" ] ; then
        UNCAPATHS=`echo $UNCAPATHS | sed -e 's/UNC\//\/\//g' | sed -e 's/\//\\/g'`
        for dir in $UNCAPATHS ; do
            ROOTDIRS="$ROOTDIRS$SEP$dir"
            SEP="|"
        done
    fi
    ALTPATHS=`ps -p $PARENTCYGPID -o args --no-headers | grep -o -E '[a-zA-Z]:/[^ ]+'`
    if [ -n "$ALTPATHS" ] ; then
        for dir in $ALTPATHS ; do
            ROOTDIRS="$ROOTDIRS$SEP$dir"
            SEP="|"
        done
    fi
    ROOTDIRS=`echo $ROOTDIRS | sed -e 's/ /\\ /g'`
    if [ -n "$ROOTDIRS" ] ; then
        ARGPAT="(^($ROOTDIRS))"
    else
        ARGPAT="(^.)"
    fi
    # Add a few more directories to the list of things to be converted
    ARGPAT="$ARGPAT|(^/tmp/)|(^/var/tmp/)"
    # Add the GRADLE_USER_HOME directory, if it's not under one of the default root dirs
    if [ -n "$GRADLE_USER_HOME" -a `echo $GRADLE_USER_HOME | grep -v -E "^($ROOTDIRS)"` ] ; then
        ARGPAT="$ARGPAT|(^$GRADLE_USER_HOME)"
    fi
fi

# Split up the JVM options only if the variable is not quoted
if [ -z "${GRADLE_OPTS+x}" ] ; then
    # The variable is not set, so we can't use it.
    # This is not a problem, just a normal case.
    :
elif [ -z "$GRADLE_OPTS" ] ; then
    # The variable is set but is empty.
    # This is not a problem, just a normal case.
    :
else
    # The variable is set and not empty.
    # We must now check if it is quoted.
    if [ `expr "x$GRADLE_OPTS" : 'x"\(.*\)"'` -ne 0 ] ; then
        # The variable is quoted, so we must not split it.
        JVM_OPTS="$JVM_OPTS $GRADLE_OPTS"
    else
        # The variable is not quoted, so we must split it.
        # This is the legacy behavior.
        # We disable globbing to avoid pathname expansion.
        set -f
        # We split the string by spaces.
        for opt in $GRADLE_OPTS ; do
            JVM_OPTS="$JVM_OPTS $opt"
        done
        # We enable globbing again.
        set +f
    fi
fi

# Add the default JVM options.
# We disable globbing to avoid pathname expansion.
set -f
# We split the string by spaces.
for opt in $DEFAULT_JVM_OPTS ; do
    JVM_OPTS="$JVM_OPTS $opt"
done
# We enable globbing again.
set +f

# Add the JAVA_OPTS.
# We disable globbing to avoid pathname expansion.
set -f
# We split the string by spaces.
for opt in $JAVA_OPTS ; do
    JVM_OPTS="$JVM_OPTS $opt"
done
# We enable globbing again.
set +f

# Add the classpath.
CLASSPATH="$WRAPPER_JAR"

# Execute the application and return its exit code
exec "$JAVACMD" \
    $JVM_OPTS \
    -classpath "$CLASSPATH" \
    org.gradle.wrapper.GradleWrapperMain "$@"