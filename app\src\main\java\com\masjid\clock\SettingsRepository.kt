
package com.masjid.clock

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

private val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = "settings")

class SettingsRepository(private val context: Context) {

    private object PreferencesKeys {
        val MOSQUE_NAME = stringPreferencesKey("mosque_name")
        val ANNOUNCEMENT = stringPreferencesKey("announcement")
        val BACKGROUND_COLOR = stringPreferencesKey("background_color")
        val TEXT_COLOR = stringPreferencesKey("text_color")
        val IQAMA_TIMES = stringPreferencesKey("iqama_times")
        val BACKGROUND_IMAGE_URI = stringPreferencesKey("background_image_uri")
        val FONT_SIZE = stringPreferencesKey("font_size")

        // New settings
        val IS_24_HOUR_FORMAT = stringPreferencesKey("is_24_hour_format")
        val ORIENTATION_MODE = stringPreferencesKey("orientation_mode") // auto, portrait, landscape
        val THEME_MODE = stringPreferencesKey("theme_mode") // light, dark, auto
        val PRIMARY_COLOR = stringPreferencesKey("primary_color")
        val SECONDARY_COLOR = stringPreferencesKey("secondary_color")
        val ACCENT_COLOR = stringPreferencesKey("accent_color")
        val FONT_FAMILY = stringPreferencesKey("font_family")
        val SHOW_HIJRI_DATE = stringPreferencesKey("show_hijri_date")
        val SHOW_GREGORIAN_DATE = stringPreferencesKey("show_gregorian_date")
        val ADHAN_ENABLED = stringPreferencesKey("adhan_enabled")
        val ADHAN_VOLUME = stringPreferencesKey("adhan_volume")
        val CUSTOM_ADHAN_URI = stringPreferencesKey("custom_adhan_uri")
        val LOCATION_LATITUDE = stringPreferencesKey("location_latitude")
        val LOCATION_LONGITUDE = stringPreferencesKey("location_longitude")
        val LOCATION_NAME = stringPreferencesKey("location_name")
        val CALCULATION_METHOD = stringPreferencesKey("calculation_method")
        val MADHAB = stringPreferencesKey("madhab")
        val FAJR_ADJUSTMENT = stringPreferencesKey("fajr_adjustment")
        val DHUHR_ADJUSTMENT = stringPreferencesKey("dhuhr_adjustment")
        val ASR_ADJUSTMENT = stringPreferencesKey("asr_adjustment")
        val MAGHRIB_ADJUSTMENT = stringPreferencesKey("maghrib_adjustment")
        val ISHA_ADJUSTMENT = stringPreferencesKey("isha_adjustment")
        val SHOW_IQAMA_COUNTDOWN = stringPreferencesKey("show_iqama_countdown")
        val IQAMA_COUNTDOWN_SIZE = stringPreferencesKey("iqama_countdown_size")
        val SCREEN_DIMMING_TIME = stringPreferencesKey("screen_dimming_time")
        val AUTO_HIDE_SCREEN = stringPreferencesKey("auto_hide_screen")
        val WEATHER_ENABLED = stringPreferencesKey("weather_enabled")
        val WEATHER_API_KEY = stringPreferencesKey("weather_api_key")
        val CUSTOM_SOUNDS_ENABLED = stringPreferencesKey("custom_sounds_enabled")
        val BACKGROUND_GRADIENT_ENABLED = stringPreferencesKey("background_gradient_enabled")
        val GRADIENT_START_COLOR = stringPreferencesKey("gradient_start_color")
        val GRADIENT_END_COLOR = stringPreferencesKey("gradient_end_color")
    }

    val mosqueNameFlow: Flow<String> = context.dataStore.data
        .map { preferences ->
            preferences[PreferencesKeys.MOSQUE_NAME] ?: "My Mosque"
        }

    suspend fun saveMosqueName(name: String) {
        context.dataStore.edit {
            it[PreferencesKeys.MOSQUE_NAME] = name
        }
    }

    val announcementFlow: Flow<String> = context.dataStore.data
        .map { preferences ->
            preferences[PreferencesKeys.ANNOUNCEMENT] ?: "Welcome"
        }

    suspend fun saveAnnouncement(announcement: String) {
        context.dataStore.edit {
            it[PreferencesKeys.ANNOUNCEMENT] = announcement
        }
    }

    val backgroundColorFlow: Flow<String> = context.dataStore.data
        .map { preferences ->
            preferences[PreferencesKeys.BACKGROUND_COLOR] ?: "#FFFFFF"
        }

    suspend fun saveBackgroundColor(color: String) {
        context.dataStore.edit {
            it[PreferencesKeys.BACKGROUND_COLOR] = color
        }
    }

    val textColorFlow: Flow<String> = context.dataStore.data
        .map { preferences ->
            preferences[PreferencesKeys.TEXT_COLOR] ?: "#000000"
        }

    suspend fun saveTextColor(color: String) {
        context.dataStore.edit {
            it[PreferencesKeys.TEXT_COLOR] = color
        }
    }

    val iqamaTimesFlow: Flow<String> = context.dataStore.data
        .map { preferences ->
            preferences[PreferencesKeys.IQAMA_TIMES] ?: "10,10,10,10,10"
        }

    suspend fun saveIqamaTimes(iqamaTimes: String) {
        context.dataStore.edit {
            it[PreferencesKeys.IQAMA_TIMES] = iqamaTimes
        }
    }

    val backgroundImageUriFlow: Flow<String> = context.dataStore.data
        .map { preferences ->
            preferences[PreferencesKeys.BACKGROUND_IMAGE_URI] ?: ""
        }

    suspend fun saveBackgroundImageUri(uri: String) {
        context.dataStore.edit {
            it[PreferencesKeys.BACKGROUND_IMAGE_URI] = uri
        }
    }

    val fontSizeFlow: Flow<String> = context.dataStore.data
        .map { preferences ->
            preferences[PreferencesKeys.FONT_SIZE] ?: "20"
        }

    suspend fun saveFontSize(size: String) {
        context.dataStore.edit {
            it[PreferencesKeys.FONT_SIZE] = size
        }
    }

    // New settings flows and save functions
    val is24HourFormatFlow: Flow<Boolean> = context.dataStore.data
        .map { preferences ->
            preferences[PreferencesKeys.IS_24_HOUR_FORMAT]?.toBoolean() ?: false
        }

    suspend fun saveIs24HourFormat(is24Hour: Boolean) {
        context.dataStore.edit {
            it[PreferencesKeys.IS_24_HOUR_FORMAT] = is24Hour.toString()
        }
    }

    val orientationModeFlow: Flow<String> = context.dataStore.data
        .map { preferences ->
            preferences[PreferencesKeys.ORIENTATION_MODE] ?: "auto"
        }

    suspend fun saveOrientationMode(mode: String) {
        context.dataStore.edit {
            it[PreferencesKeys.ORIENTATION_MODE] = mode
        }
    }

    val themeModeFlow: Flow<String> = context.dataStore.data
        .map { preferences ->
            preferences[PreferencesKeys.THEME_MODE] ?: "auto"
        }

    suspend fun saveThemeMode(mode: String) {
        context.dataStore.edit {
            it[PreferencesKeys.THEME_MODE] = mode
        }
    }

    val primaryColorFlow: Flow<String> = context.dataStore.data
        .map { preferences ->
            preferences[PreferencesKeys.PRIMARY_COLOR] ?: "#6200EE"
        }

    suspend fun savePrimaryColor(color: String) {
        context.dataStore.edit {
            it[PreferencesKeys.PRIMARY_COLOR] = color
        }
    }

    val secondaryColorFlow: Flow<String> = context.dataStore.data
        .map { preferences ->
            preferences[PreferencesKeys.SECONDARY_COLOR] ?: "#03DAC6"
        }

    suspend fun saveSecondaryColor(color: String) {
        context.dataStore.edit {
            it[PreferencesKeys.SECONDARY_COLOR] = color
        }
    }

    val accentColorFlow: Flow<String> = context.dataStore.data
        .map { preferences ->
            preferences[PreferencesKeys.ACCENT_COLOR] ?: "#BB86FC"
        }

    suspend fun saveAccentColor(color: String) {
        context.dataStore.edit {
            it[PreferencesKeys.ACCENT_COLOR] = color
        }
    }

    val fontFamilyFlow: Flow<String> = context.dataStore.data
        .map { preferences ->
            preferences[PreferencesKeys.FONT_FAMILY] ?: "default"
        }

    suspend fun saveFontFamily(fontFamily: String) {
        context.dataStore.edit {
            it[PreferencesKeys.FONT_FAMILY] = fontFamily
        }
    }

    val showHijriDateFlow: Flow<Boolean> = context.dataStore.data
        .map { preferences ->
            preferences[PreferencesKeys.SHOW_HIJRI_DATE]?.toBoolean() ?: true
        }

    suspend fun saveShowHijriDate(show: Boolean) {
        context.dataStore.edit {
            it[PreferencesKeys.SHOW_HIJRI_DATE] = show.toString()
        }
    }

    val showGregorianDateFlow: Flow<Boolean> = context.dataStore.data
        .map { preferences ->
            preferences[PreferencesKeys.SHOW_GREGORIAN_DATE]?.toBoolean() ?: true
        }

    suspend fun saveShowGregorianDate(show: Boolean) {
        context.dataStore.edit {
            it[PreferencesKeys.SHOW_GREGORIAN_DATE] = show.toString()
        }
    }

    val adhanEnabledFlow: Flow<Boolean> = context.dataStore.data
        .map { preferences ->
            preferences[PreferencesKeys.ADHAN_ENABLED]?.toBoolean() ?: true
        }

    suspend fun saveAdhanEnabled(enabled: Boolean) {
        context.dataStore.edit {
            it[PreferencesKeys.ADHAN_ENABLED] = enabled.toString()
        }
    }

    val adhanVolumeFlow: Flow<Float> = context.dataStore.data
        .map { preferences ->
            preferences[PreferencesKeys.ADHAN_VOLUME]?.toFloat() ?: 0.8f
        }

    suspend fun saveAdhanVolume(volume: Float) {
        context.dataStore.edit {
            it[PreferencesKeys.ADHAN_VOLUME] = volume.toString()
        }
    }

    val customAdhanUriFlow: Flow<String> = context.dataStore.data
        .map { preferences ->
            preferences[PreferencesKeys.CUSTOM_ADHAN_URI] ?: ""
        }

    suspend fun saveCustomAdhanUri(uri: String) {
        context.dataStore.edit {
            it[PreferencesKeys.CUSTOM_ADHAN_URI] = uri
        }
    }

    val locationLatitudeFlow: Flow<Double> = context.dataStore.data
        .map { preferences ->
            preferences[PreferencesKeys.LOCATION_LATITUDE]?.toDouble() ?: 21.3891
        }

    suspend fun saveLocationLatitude(latitude: Double) {
        context.dataStore.edit {
            it[PreferencesKeys.LOCATION_LATITUDE] = latitude.toString()
        }
    }

    val locationLongitudeFlow: Flow<Double> = context.dataStore.data
        .map { preferences ->
            preferences[PreferencesKeys.LOCATION_LONGITUDE]?.toDouble() ?: 39.8579
        }

    suspend fun saveLocationLongitude(longitude: Double) {
        context.dataStore.edit {
            it[PreferencesKeys.LOCATION_LONGITUDE] = longitude.toString()
        }
    }

    val locationNameFlow: Flow<String> = context.dataStore.data
        .map { preferences ->
            preferences[PreferencesKeys.LOCATION_NAME] ?: "Mecca"
        }

    suspend fun saveLocationName(name: String) {
        context.dataStore.edit {
            it[PreferencesKeys.LOCATION_NAME] = name
        }
    }
}
