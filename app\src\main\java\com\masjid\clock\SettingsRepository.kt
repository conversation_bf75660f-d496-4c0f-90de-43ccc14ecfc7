
package com.masjid.clock

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

private val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = "settings")

class SettingsRepository(private val context: Context) {

    private object PreferencesKeys {
        val MOSQUE_NAME = stringPreferencesKey("mosque_name")
        val ANNOUNCEMENT = stringPreferencesKey("announcement")
        val BACKGROUND_COLOR = stringPreferencesKey("background_color")
        val TEXT_COLOR = stringPreferencesKey("text_color")
        val IQAMA_TIMES = stringPreferencesKey("iqama_times")
        val BACKGROUND_IMAGE_URI = stringPreferencesKey("background_image_uri")
        val FONT_SIZE = stringPreferencesKey("font_size")
    }

    val mosqueNameFlow: Flow<String> = context.dataStore.data
        .map { preferences ->
            preferences[PreferencesKeys.MOSQUE_NAME] ?: "My Mosque"
        }

    suspend fun saveMosqueName(name: String) {
        context.dataStore.edit {
            it[PreferencesKeys.MOSQUE_NAME] = name
        }
    }

    val announcementFlow: Flow<String> = context.dataStore.data
        .map { preferences ->
            preferences[PreferencesKeys.ANNOUNCEMENT] ?: "Welcome"
        }

    suspend fun saveAnnouncement(announcement: String) {
        context.dataStore.edit {
            it[PreferencesKeys.ANNOUNCEMENT] = announcement
        }
    }

    val backgroundColorFlow: Flow<String> = context.dataStore.data
        .map { preferences ->
            preferences[PreferencesKeys.BACKGROUND_COLOR] ?: "#FFFFFF"
        }

    suspend fun saveBackgroundColor(color: String) {
        context.dataStore.edit {
            it[PreferencesKeys.BACKGROUND_COLOR] = color
        }
    }

    val textColorFlow: Flow<String> = context.dataStore.data
        .map { preferences ->
            preferences[PreferencesKeys.TEXT_COLOR] ?: "#000000"
        }

    suspend fun saveTextColor(color: String) {
        context.dataStore.edit {
            it[PreferencesKeys.TEXT_COLOR] = color
        }
    }

    val iqamaTimesFlow: Flow<String> = context.dataStore.data
        .map { preferences ->
            preferences[PreferencesKeys.IQAMA_TIMES] ?: "10,10,10,10,10"
        }

    suspend fun saveIqamaTimes(iqamaTimes: String) {
        context.dataStore.edit {
            it[PreferencesKeys.IQAMA_TIMES] = iqamaTimes
        }
    }

    val backgroundImageUriFlow: Flow<String> = context.dataStore.data
        .map { preferences ->
            preferences[PreferencesKeys.BACKGROUND_IMAGE_URI] ?: ""
        }

    suspend fun saveBackgroundImageUri(uri: String) {
        context.dataStore.edit {
            it[PreferencesKeys.BACKGROUND_IMAGE_URI] = uri
        }
    }

    val fontSizeFlow: Flow<String> = context.dataStore.data
        .map { preferences ->
            preferences[PreferencesKeys.FONT_SIZE] ?: "20"
        }

    suspend fun saveFontSize(size: String) {
        context.dataStore.edit {
            it[PreferencesKeys.FONT_SIZE] = size
        }
    }
}
