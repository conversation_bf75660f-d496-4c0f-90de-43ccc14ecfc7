package com.masjid.clock

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Color.parseColor
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.viewModels
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Settings
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.app.ActivityCompat
import androidx.lifecycle.viewmodel.compose.viewModel
import coil.compose.rememberImagePainter
import com.google.android.gms.location.FusedLocationProviderClient
import com.google.android.gms.location.LocationServices
import kotlinx.coroutines.delay
import java.text.SimpleDateFormat
import java.util.*

class MainActivity : ComponentActivity() {

    private lateinit var fusedLocationClient: FusedLocationProviderClient

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        fusedLocationClient = LocationServices.getFusedLocationProviderClient(this)

        if (ActivityCompat.checkSelfPermission(
                this,
                Manifest.permission.ACCESS_FINE_LOCATION
            ) != PackageManager.PERMISSION_GRANTED && ActivityCompat.checkSelfPermission(
                this,
                Manifest.permission.ACCESS_COARSE_LOCATION
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            ActivityCompat.requestPermissions(
                this,
                arrayOf(Manifest.permission.ACCESS_FINE_LOCATION, Manifest.permission.ACCESS_COARSE_LOCATION),
                101
            )
        }

        setContent {
            MasjidClockTheme {
                val viewModel: MainViewModel by viewModels()
                val backgroundColor by viewModel.backgroundColor.collectAsState()
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = Color(parseColor(backgroundColor))
                ) {
                    MasjidClockScreen(viewModel)
                }
            }
        }
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<out String>, grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == 101) {
            if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                if (ActivityCompat.checkSelfPermission(
                        this,
                        Manifest.permission.ACCESS_FINE_LOCATION
                    ) == PackageManager.PERMISSION_GRANTED && ActivityCompat.checkSelfPermission(
                        this,
                        Manifest.permission.ACCESS_COARSE_LOCATION
                    ) == PackageManager.PERMISSION_GRANTED
                ) {
                    fusedLocationClient.lastLocation.addOnSuccessListener { location ->
                        location?.let {
                            val viewModel: MainViewModel by viewModels()
                            viewModel.updateLocation(it.latitude, it.longitude)
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun MasjidClockScreen(viewModel: MainViewModel) {
    val currentTime by viewModel.currentTime.collectAsState()
    val currentDate by viewModel.currentDate.collectAsState()
    val prayerTimes by viewModel.prayerTimes.collectAsState()
    val hijriDate by viewModel.hijriDate.collectAsState()
    val mosqueName by viewModel.mosqueName.collectAsState()
    val announcement by viewModel.announcement.collectAsState()
    val textColor by viewModel.textColor.collectAsState()
    val iqamaTimes by viewModel.iqamaTimes.collectAsState()
    val backgroundImageUri by viewModel.backgroundImageUri.collectAsState()
    val weather by viewModel.weather.collectAsState()

    val timeFormatter = SimpleDateFormat("hh:mm a", Locale.getDefault())

    val iqamaTimesList = iqamaTimes.split(",").map { it.trim().toInt() }

    var nextPrayerName by remember { mutableStateOf("") }
    var nextPrayerTime by remember { mutableStateOf(0L) }
    var iqamaCountdown by remember { mutableStateOf(0L) }

    prayerTimes?.let {
        val now = System.currentTimeMillis()
        val prayerList = listOf(
            "Fajr" to it.fajr.time,
            "Dhuhr" to it.dhuhr.time,
            "Asr" to it.asr.time,
            "Maghrib" to it.maghrib.time,
            "Isha" to it.isha.time
        )

        val nextPrayer = prayerList.firstOrNull { (_, time) -> time > now }

        if (nextPrayer != null) {
            nextPrayerName = nextPrayer.first
            nextPrayerTime = nextPrayer.second

            val iqamaTime = iqamaTimesList[prayerList.indexOf(nextPrayer)] * 60 * 1000
            val prayerIqamaTime = nextPrayerTime + iqamaTime

            LaunchedEffect(key1 = nextPrayerTime) {
                while (System.currentTimeMillis() < prayerIqamaTime) {
                    iqamaCountdown = prayerIqamaTime - System.currentTimeMillis()
                    delay(1000)
                }
            }
        }
    }

    val fontSize by viewModel.fontSize.collectAsState()

    val fontSizeSp = fontSize.toInt().sp

    Box(modifier = Modifier.fillMaxSize()) {
        // ... rest of the code

            // Top Section
            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                Text(text = mosqueName, fontSize = fontSizeSp + 4.sp, fontWeight = FontWeight.Bold, color = Color(parseColor(textColor)))
                Spacer(modifier = Modifier.height(16.dp))
                // Top Row
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.Top
                ) {
                    // Fajr Time
                    prayerTimes?.let {
                        PrayerTimeCard(prayerName = "Fajr", prayerTime = timeFormatter.format(it.fajr), textColor = Color(parseColor(textColor)), fontSize = fontSizeSp)
                    }

                    // Center Clock
                    Column(horizontalAlignment = Alignment.CenterHorizontally) {
                        Text(text = currentTime, fontSize = fontSizeSp + 28.sp, fontWeight = FontWeight.Bold, color = Color(parseColor(textColor)))
                        Text(text = currentDate, fontSize = fontSizeSp, color = Color(parseColor(textColor)))
                        Text(text = hijriDate, fontSize = fontSizeSp, color = Color(parseColor(textColor)))
                        Text(text = "Saturday", fontSize = fontSizeSp, color = Color(parseColor(textColor)))
                    }

                    // Temperature and Sunrise
                    Column(horizontalAlignment = Alignment.End) {
                        weather?.let {
                            Text(text = "${it.main.temp}°C", fontSize = fontSizeSp, color = Color(parseColor(textColor)))
                        } ?: Text(text = "--°C", fontSize = fontSizeSp, color = Color(parseColor(textColor)))
                        Spacer(modifier = Modifier.height(8.dp))
                        prayerTimes?.let {
                            Text(text = "Sunrise: ${timeFormatter.format(it.sunrise)}", fontSize = fontSizeSp - 4.sp, color = Color(parseColor(textColor)))
                        }
                    }
                }
            }

            // Iqama Countdown
            if (iqamaCountdown > 0) {
                val minutes = (iqamaCountdown / 1000) / 60
                val seconds = (iqamaCountdown / 1000) % 60
                Text(
                    text = "Iqama for $nextPrayerName in ${minutes}m ${seconds}s",
                    fontSize = fontSizeSp + 4.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.Red
                )
            }

            // Announcement
            Text(text = announcement, fontSize = fontSizeSp, modifier = Modifier.padding(16.dp), color = Color(parseColor(textColor)))

            // Bottom Prayer Times
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalArrangement = Arrangement.SpaceAround
            ) {
                prayerTimes?.let {
                    PrayerTimeCard(prayerName = "Dhuhr", prayerTime = timeFormatter.format(it.dhuhr), textColor = Color(parseColor(textColor)), fontSize = fontSizeSp)
                    PrayerTimeCard(prayerName = "Asr", prayerTime = timeFormatter.format(it.asr), textColor = Color(parseColor(textColor)), fontSize = fontSizeSp)
                    PrayerTimeCard(prayerName = "Maghrib", prayerTime = timeFormatter.format(it.maghrib), textColor = Color(parseColor(textColor)), fontSize = fontSizeSp)
                    PrayerTimeCard(prayerName = "Isha", prayerTime = timeFormatter.format(it.isha), textColor = Color(parseColor(textColor)), fontSize = fontSizeSp)
                }
            }
        }

        // ... rest of the code
    }
}

@Composable
fun PrayerTimeCard(prayerName: String, prayerTime: String, textColor: Color) {
    Card(
        modifier = Modifier.padding(8.dp),
        elevation = 4.dp
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(text = prayerName, fontWeight = FontWeight.Bold, color = textColor)
            Text(text = prayerTime, color = textColor)
        }
    }
}

@Preview(showBackground = true)
@Composable
fun DefaultPreview() {
    MasjidClockTheme {
        //MasjidClockScreen(viewModel())
    }
}

@Composable
fun MasjidClockTheme(content: @Composable () -> Unit) {
    MaterialTheme(
        typography = MaterialTheme.typography,
        shapes = MaterialTheme.shapes,
        content = content
    )
}