{"logs": [{"outputFile": "com.masjid.clock.app-mergeDebugResources-55:/values-ja/values-ja.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\6afa17b2b3e5aa97934ff0de89083c5d\\transformed\\material-1.8.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,253,320,384,453,534,616,701,805,881,944,1028,1092,1150,1231,1292,1356,1411,1470,1527,1581,1674,1730,1787,1841,1907,2007,2083,2164,2286,2348,2410,2489,2542,2593,2659,2729,2799,2876,2940,3011,3079,3142,3221,3284,3364,3446,3518,3589,3661,3709,3773,3848,3925,3987,4051,4114,4200,4284,4365,4450,4507,4562", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,66,63,68,80,81,84,103,75,62,83,63,57,80,60,63,54,58,56,53,92,55,56,53,65,99,75,80,121,61,61,78,52,50,65,69,69,76,63,70,67,62,78,62,79,81,71,70,71,47,63,74,76,61,63,62,85,83,80,84,56,54,72", "endOffsets": "248,315,379,448,529,611,696,800,876,939,1023,1087,1145,1226,1287,1351,1406,1465,1522,1576,1669,1725,1782,1836,1902,2002,2078,2159,2281,2343,2405,2484,2537,2588,2654,2724,2794,2871,2935,3006,3074,3137,3216,3279,3359,3441,3513,3584,3656,3704,3768,3843,3920,3982,4046,4109,4195,4279,4360,4445,4502,4557,4630"}, "to": {"startLines": "2,33,34,35,36,37,38,39,40,46,47,48,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,105", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2889,2956,3020,3089,3170,3252,3337,3441,3950,4013,4097,4317,4375,4456,4517,4581,4636,4695,4752,4806,4899,4955,5012,5066,5132,5232,5308,5389,5511,5573,5635,5714,5767,5818,5884,5954,6024,6101,6165,6236,6304,6367,6446,6509,6589,6671,6743,6814,6886,6934,6998,7073,7150,7212,7276,7339,7425,7509,7590,7675,7732,8075", "endLines": "5,33,34,35,36,37,38,39,40,46,47,48,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,105", "endColumns": "12,66,63,68,80,81,84,103,75,62,83,63,57,80,60,63,54,58,56,53,92,55,56,53,65,99,75,80,121,61,61,78,52,50,65,69,69,76,63,70,67,62,78,62,79,81,71,70,71,47,63,74,76,61,63,62,85,83,80,84,56,54,72", "endOffsets": "298,2951,3015,3084,3165,3247,3332,3436,3512,4008,4092,4156,4370,4451,4512,4576,4631,4690,4747,4801,4894,4950,5007,5061,5127,5227,5303,5384,5506,5568,5630,5709,5762,5813,5879,5949,6019,6096,6160,6231,6299,6362,6441,6504,6584,6666,6738,6809,6881,6929,6993,7068,7145,7207,7271,7334,7420,7504,7585,7670,7727,7782,8143"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\9fd9d7009dd2359dbdb604cdcc345b0a\\transformed\\jetified-ui-1.4.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,191,269,358,455,538,616,694,779,854,918,982,1056,1132,1201,1277,1342", "endColumns": "85,77,88,96,82,77,77,84,74,63,63,73,75,68,75,64,116", "endOffsets": "186,264,353,450,533,611,689,774,849,913,977,1051,1127,1196,1272,1337,1454"}, "to": {"startLines": "41,42,43,44,45,49,50,101,102,103,104,106,107,109,111,112,113", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3517,3603,3681,3770,3867,4161,4239,7787,7872,7947,8011,8148,8222,8377,8547,8623,8688", "endColumns": "85,77,88,96,82,77,77,84,74,63,63,73,75,68,75,64,116", "endOffsets": "3598,3676,3765,3862,3945,4234,4312,7867,7942,8006,8070,8217,8293,8441,8618,8683,8800"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\39edba29d1252d0b67d5e84f709fb54f\\transformed\\appcompat-1.6.1\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,295,400,482,580,688,766,841,932,1025,1120,1214,1314,1407,1502,1596,1687,1778,1856,1958,2056,2151,2254,2350,2446,2594,2691", "endColumns": "96,92,104,81,97,107,77,74,90,92,94,93,99,92,94,93,90,90,77,101,97,94,102,95,95,147,96,78", "endOffsets": "197,290,395,477,575,683,761,836,927,1020,1115,1209,1309,1402,1497,1591,1682,1773,1851,1953,2051,2146,2249,2345,2441,2589,2686,2765"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "303,400,493,598,680,778,886,964,1039,1130,1223,1318,1412,1512,1605,1700,1794,1885,1976,2054,2156,2254,2349,2452,2548,2644,2792,8298", "endColumns": "96,92,104,81,97,107,77,74,90,92,94,93,99,92,94,93,90,90,77,101,97,94,102,95,95,147,96,78", "endOffsets": "395,488,593,675,773,881,959,1034,1125,1218,1313,1407,1507,1600,1695,1789,1880,1971,2049,2151,2249,2344,2447,2543,2639,2787,2884,8372"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\f7c8c22bdafe0bf1b7b8f05029141cd9\\transformed\\core-1.9.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "110", "startColumns": "4", "startOffsets": "8446", "endColumns": "100", "endOffsets": "8542"}}]}]}