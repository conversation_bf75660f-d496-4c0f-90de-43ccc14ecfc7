package com.masjid.clock.ui.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.rememberAsyncImagePainter
import com.masjid.clock.MainViewModel
import com.masjid.clock.models.DailyPrayerTimes
import com.masjid.clock.models.PrayerTimeData
import java.text.SimpleDateFormat
import java.util.*

@Composable
fun MasjidClockMainScreen(
    viewModel: MainViewModel,
    modifier: Modifier = Modifier
) {
    val configuration = LocalConfiguration.current
    val isLandscape = configuration.screenWidthDp > configuration.screenHeightDp

    // Collect all state
    val currentTime by viewModel.currentTime.collectAsState()
    val currentDate by viewModel.currentDate.collectAsState()
    val hijriDate by viewModel.hijriDate.collectAsState()
    val mosqueName by viewModel.mosqueName.collectAsState()
    val announcement by viewModel.announcement.collectAsState()
    val dailyPrayerTimes by viewModel.dailyPrayerTimes.collectAsState()
    val iqamaCountdown by viewModel.iqamaCountdown.collectAsState()
    val currentPrayerStatus by viewModel.currentPrayerStatus.collectAsState()
    val weather by viewModel.weather.collectAsState()
    
    // Theme colors
    val backgroundColor by viewModel.backgroundColor.collectAsState()
    val textColor by viewModel.textColor.collectAsState()
    val primaryColor by viewModel.primaryColor.collectAsState()
    val secondaryColor by viewModel.secondaryColor.collectAsState()
    val fontSize by viewModel.fontSize.collectAsState()
    val backgroundImageUri by viewModel.backgroundImageUri.collectAsState()
    val showHijriDate by viewModel.showHijriDate.collectAsState()
    val showGregorianDate by viewModel.showGregorianDate.collectAsState()

    Box(
        modifier = modifier.fillMaxSize()
    ) {
        // Background
        BackgroundLayer(
            backgroundImageUri = backgroundImageUri,
            backgroundColor = backgroundColor,
            primaryColor = primaryColor,
            secondaryColor = secondaryColor
        )

        // Main content
        if (isLandscape) {
            LandscapeLayout(
                currentTime = currentTime,
                currentDate = currentDate,
                hijriDate = hijriDate,
                mosqueName = mosqueName,
                announcement = announcement,
                dailyPrayerTimes = dailyPrayerTimes,
                iqamaCountdown = iqamaCountdown,
                currentPrayerStatus = currentPrayerStatus,
                weather = weather,
                textColor = textColor,
                primaryColor = primaryColor,
                fontSize = fontSize,
                showHijriDate = showHijriDate,
                showGregorianDate = showGregorianDate
            )
        } else {
            PortraitLayout(
                currentTime = currentTime,
                currentDate = currentDate,
                hijriDate = hijriDate,
                mosqueName = mosqueName,
                announcement = announcement,
                dailyPrayerTimes = dailyPrayerTimes,
                iqamaCountdown = iqamaCountdown,
                currentPrayerStatus = currentPrayerStatus,
                weather = weather,
                textColor = textColor,
                primaryColor = primaryColor,
                fontSize = fontSize,
                showHijriDate = showHijriDate,
                showGregorianDate = showGregorianDate
            )
        }
    }
}

@Composable
private fun BackgroundLayer(
    backgroundImageUri: String,
    backgroundColor: String,
    primaryColor: String,
    secondaryColor: String
) {
    Box(modifier = Modifier.fillMaxSize()) {
        if (backgroundImageUri.isNotEmpty()) {
            Image(
                painter = rememberAsyncImagePainter(backgroundImageUri),
                contentDescription = null,
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.Crop
            )
            // Overlay for better text readability
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Black.copy(alpha = 0.3f))
            )
        } else {
            // Gradient background
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(
                        brush = Brush.verticalGradient(
                            colors = listOf(
                                Color(android.graphics.Color.parseColor(backgroundColor)),
                                Color(android.graphics.Color.parseColor(primaryColor)).copy(alpha = 0.8f),
                                Color(android.graphics.Color.parseColor(secondaryColor)).copy(alpha = 0.6f)
                            )
                        )
                    )
            )
        }
    }
}

@Composable
private fun PortraitLayout(
    currentTime: String,
    currentDate: String,
    hijriDate: String,
    mosqueName: String,
    announcement: String,
    dailyPrayerTimes: DailyPrayerTimes?,
    iqamaCountdown: String,
    currentPrayerStatus: String,
    weather: com.masjid.clock.models.WeatherData?,
    textColor: String,
    primaryColor: String,
    fontSize: String,
    showHijriDate: Boolean,
    showGregorianDate: Boolean
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // Header with mosque name
        MosqueHeader(
            mosqueName = mosqueName,
            textColor = textColor,
            fontSize = fontSize
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // Main clock section
        MainClockSection(
            currentTime = currentTime,
            currentDate = currentDate,
            hijriDate = hijriDate,
            textColor = textColor,
            fontSize = fontSize,
            showHijriDate = showHijriDate,
            showGregorianDate = showGregorianDate,
            weather = weather
        )
        
        Spacer(modifier = Modifier.height(32.dp))
        
        // Prayer times grid
        dailyPrayerTimes?.let { prayers ->
            PrayerTimesGrid(
                prayerTimes = prayers,
                textColor = textColor,
                primaryColor = primaryColor,
                fontSize = fontSize,
                isLandscape = false
            )
        }
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // Iqama countdown
        if (iqamaCountdown.isNotEmpty()) {
            IqamaCountdownSection(
                countdown = iqamaCountdown,
                prayerStatus = currentPrayerStatus,
                textColor = textColor,
                fontSize = fontSize
            )
        }
        
        Spacer(modifier = Modifier.weight(1f))
        
        // Announcement
        if (announcement.isNotEmpty()) {
            AnnouncementSection(
                announcement = announcement,
                textColor = textColor,
                fontSize = fontSize
            )
        }
    }
}

@Composable
private fun LandscapeLayout(
    currentTime: String,
    currentDate: String,
    hijriDate: String,
    mosqueName: String,
    announcement: String,
    dailyPrayerTimes: DailyPrayerTimes?,
    iqamaCountdown: String,
    currentPrayerStatus: String,
    weather: com.masjid.clock.models.WeatherData?,
    textColor: String,
    primaryColor: String,
    fontSize: String,
    showHijriDate: Boolean,
    showGregorianDate: Boolean
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // Header
        MosqueHeader(
            mosqueName = mosqueName,
            textColor = textColor,
            fontSize = fontSize
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            // Left side - Prayer times
            Column(
                modifier = Modifier.weight(1f)
            ) {
                dailyPrayerTimes?.let { prayers ->
                    PrayerTimesGrid(
                        prayerTimes = prayers,
                        textColor = textColor,
                        primaryColor = primaryColor,
                        fontSize = fontSize,
                        isLandscape = true
                    )
                }
            }
            
            // Center - Main clock
            Column(
                modifier = Modifier.weight(1f),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                MainClockSection(
                    currentTime = currentTime,
                    currentDate = currentDate,
                    hijriDate = hijriDate,
                    textColor = textColor,
                    fontSize = fontSize,
                    showHijriDate = showHijriDate,
                    showGregorianDate = showGregorianDate,
                    weather = weather
                )
                
                if (iqamaCountdown.isNotEmpty()) {
                    Spacer(modifier = Modifier.height(16.dp))
                    IqamaCountdownSection(
                        countdown = iqamaCountdown,
                        prayerStatus = currentPrayerStatus,
                        textColor = textColor,
                        fontSize = fontSize
                    )
                }
            }
            
            // Right side - Weather and announcement
            Column(
                modifier = Modifier.weight(1f),
                horizontalAlignment = Alignment.End
            ) {
                weather?.let {
                    WeatherSection(
                        weather = it,
                        textColor = textColor,
                        fontSize = fontSize
                    )
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                if (announcement.isNotEmpty()) {
                    AnnouncementSection(
                        announcement = announcement,
                        textColor = textColor,
                        fontSize = fontSize
                    )
                }
            }
        }
    }
}
